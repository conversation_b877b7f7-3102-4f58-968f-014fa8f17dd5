<template>
  <div style="min-width:1651px;box-sizing:border-box;margin:0 auto;">
    <div>
      <div style="width:100%;height:37px;" class="flex align-items-center justify-content-between box-sizing">
        <div class="flex align-items-center cursor-pointer">
          <div :class="[
            tapMenuValue == 0 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" @click.stop="tapMenu(0)">
            今日订单
            <div class="tapMenuBadge" v-if="badgeList.daiPaiDan">
              <span>{{ badgeList.daiPaiDan }}</span>
            </div>
            <!-- <div class="tapMenuBadge" v-if="badgeList.today">
              <span>{{ badgeList.today }}</span>
            </div> -->
          </div>
          <div :class="[
            tapMenuValue == 1 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" @click.stop="tapMenu(1)">
            昨日订单
            <div class="tapMenuBadge" v-if="badgeList.yesterday">
              <span>{{ badgeList.yesterday }}</span>
            </div>
          </div>
          <div :class="[
            tapMenuValue == 2 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" @click.stop="tapMenu(2)">
            全部
            <div class="tapMenuBadge" v-if="badgeList.all">
              <span>{{ badgeList.all }}</span>
            </div>
          </div>
          <div :class="[
            tapMenuValue == 3 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" @click.stop="tapMenu(3)">
            水站待派单
            <div class="tapMenuBadge" v-if="badgeList.daiPaiDan">
              <span>{{ badgeList.daiPaiDan }}</span>
            </div>
          </div>
          <div :class="[
            tapMenuValue == 12 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" @click.stop="tapMenu(12)">
            回退订单
            <div class="tapMenuBadge" v-if="badgeList.huitui">
              <span>{{ badgeList.huitui }}</span>
            </div>
          </div>
          <div :class="[
            tapMenuValue == 4 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" @click.stop="tapMenu(4)">
            配送中
            <div class="tapMenuBadge" v-if="badgeList.daiJieDan">
              <span>{{ badgeList.daiJieDan }}</span>
            </div>
          </div>
          <div :class="[
            tapMenuValue == 7 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" @click.stop="tapMenu(7)">
            已完成
            <div class="tapMenuBadge" v-if="badgeList.yiWanCheng">
              <span>{{ badgeList.yiWanCheng }}</span>
            </div>
          </div>
          <div :class="[
            tapMenuValue == 8 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" @click.stop="tapMenu(8)">
            待退款
            <div class="tapMenuBadge" v-if="badgeList.returnNum">
              <span>{{ badgeList.returnNum }}</span>
            </div>
          </div>
          <div :class="[
            tapMenuValue == 9 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" style="border-right: 1px solid rgba(216, 220, 229, 1)" @click.stop="tapMenu(9)">
            押桶申请中
            <div class="tapMenuBadge" v-if="badgeList.pledgeBuckApplyNum">
              <span>{{ badgeList.pledgeBuckApplyNum }}</span>
            </div>
          </div>
          <div :class="[
            tapMenuValue == 10 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" style="border-right: 1px solid rgba(216, 220, 229, 1)" @click.stop="tapMenu(10)">
            退桶
            <div class="tapMenuBadge" v-if="badgeList.bucketNum">
              <span>{{ badgeList.bucketNum }}</span>
            </div>
          </div>
          <div :class="[
            tapMenuValue == 11 ? 'active_top_box' : '',
            'top-box position-relative',
          ]" style="border-right: 1px solid rgba(216, 220, 229, 1)" @click.stop="tapMenu(11)">
            水票记录表
            <div class="tapMenuBadge" v-if="waterMark">
              <span>{{ waterMark }}</span>
            </div>
          </div>
        </div>
        <div class="font-size-18 flex align-items-center" v-if="tapMenuValue == 0">
          <div class="margin-right-20">
            今日订单总数：<span class="color-red">{{ orderSum }}</span>
          </div>
          <div>
            今日订单实付总金额<span class="color-red"><span class="font-size-16">￥</span>{{ orderSumMoney }}</span>
          </div>
        </div>

        <div class="font-size-18 flex align-items-center" v-else-if="tapMenuValue == 1">
          <div class="margin-right-20">
            昨日订单总数：<span class="color-red">{{ orderSum }}</span>
          </div>
          <div>
            昨日订单实付总金额<span class="color-red"><span class="font-size-16">￥</span>{{ orderSumMoney }}</span>
          </div>
        </div>
        <div v-else></div>
      </div>
      <div style="padding:20px; border:1px solid rgba(216,220,229,1);">
        <div v-if="tapMenuValue != 10 && tapMenuValue != 11">
          <el-select v-model="orderSelect.deliveryName" clearable style="width:148px;margin-right:20px;"
            placeholder="按送水员筛选">
            <el-option v-for="(item, index) in orderDeliveryOptions" :key="index" :label="item.name" :value="item.name">
            </el-option>
          </el-select>

          <el-select v-model="orderSelect.userType" clearable style="width:148px;margin-right:20px;"
            placeholder="按客户分类筛选">
            <el-option v-for="(item, index) in orderUserTypeOptions" :key="index" :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>

          <el-select v-model="orderSelect.payType" clearable style="width:148px;margin-right:20px;"
            placeholder="按支付方式筛选">
            <el-option v-for="(item, index) in orderPayTypeOptions" :key="index" :label="item.label"
              :value="item.value">
            </el-option>
          </el-select>
          <el-select v-model="selectVal" placeholder="按订单状态筛选" clearable style="width:148px;margin-right:20px;" v-if="tapMenuValue == 0 ||
            tapMenuValue == 1 ||
            tapMenuValue == 2 ||
            tapMenuValue == 0 ||
            tapMenuValue == 8
          ">
            <el-option v-for="(item, index) in selectOptions" :key="index" :label="item.label"
              :value="JSON.stringify(item.value)">
            </el-option>
          </el-select>
          <el-select v-model="ordersource" filterable clearable style="width:148px;margin-right:20px;">
            <el-option v-for="item in ordersourcefilter" :key="item.key" :label="item.value" :value="item.key"
              :disabled="item.disabled" :show="item.isShow"></el-option>
          </el-select>
          <div v-if="tapMenuValue != 0 && tapMenuValue != 1 && tapMenuValue != 3"
            style="display:inline-block;margin-right:20px;">
            <el-date-picker v-model="orderSelect.date" style="width:250px;" value-format="yyyy-MM-dd" type="daterange"
              align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
              :picker-options="dateSelect">
            </el-date-picker>
          </div>
          <el-input v-model="isKey" placeholder="输入订单号/手机号/联系方式/搜索" style="width:282px;margin-right:20px;"></el-input>
          <el-input v-model="isKey2" placeholder="输入地址搜索" style="width:282px;margin-right:20px;"></el-input>

          <el-button type="primary" @click="load(1)">查询</el-button>

          <el-button style="margin-left:20px;" @click="clearSearch">清空筛选条件</el-button>
          <el-button type="primary" :disabled="selectedOrders.length <= 0" style="margin-left:20px;"
            @click="batchDispatch" v-if="tapMenuValue == 3">批量派单</el-button>
          <el-button type="primary" :disabled="selectedOrders.length <= 0" style="margin-left:20px;"
            @click="batchBack" v-if="tapMenuValue == 3 || tapMenuValue == 12">批量回退</el-button>
          <el-button type="primary" :disabled="selectedOrders.length <= 0" style="margin-left:20px;"
            @click="batchDispatchChange" v-if="tapMenuValue == 4">批量改派</el-button>
          <el-button type="success" :disabled="selectedOrders.length <= 0" style="margin-left:20px;"
            @click="batchDelivered" v-if="tapMenuValue == 4">批量送达</el-button>
        </div>
      </div>
    </div>
    <div class="content_top flex align-items-center" :style="list.length > 1 ? 'padding-right:15px;' : ''"
      v-if="tapMenuValue != 10 && tapMenuValue != 11">
      <div style="min-width:394px;width:20%" class="box-sizing">商品信息</div>
      <div style="min-width:394px;width:20%" class="box-sizing">客户信息</div>
      <div style="min-width:217px;width:15%" class="box-sizing">
        管理信息
      </div>
      <div style="min-width:217px;width:15%" class="box-sizing">订单状态</div>
      <div style="min-width:217px;width:15%" class="box-sizing">送水员</div>
      <div style="min-width:217px;width:15%;border-right:none;" class="box-sizing">
        操作
      </div>
    </div>
    <!-- 押桶申请单 -->
    <div v-loading="isLoading" v-if="tapMenuValue == 10">
      <div>
        <div style="display:inline-block;margin-right:20px;">
          <el-date-picker v-model="orderSelect.date" style="width:250px;" value-format="yyyy-MM-dd" type="daterange"
            align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="dateSelect">
          </el-date-picker>
        </div>
        <el-input v-model="isKey" placeholder="输入订单号/手机号/联系方式搜索" style="width:282px;margin-right:20px;"></el-input>
        <el-input v-model="isKey2" placeholder="输入地址搜索" style="width:282px;margin-right:20px;"></el-input>

        <el-button type="primary" @click="load(1)">查询</el-button>

        <el-button style="margin-left:20px;" @click="clearSearch">清空筛选条件</el-button>
      </div>
      <el-table :data="butcketlist" key="yiwbhjsdzbzncdml" :header-cell-style="{
        'text-align': 'center',
        'background-color': '#EFF2F7',
      }" :cell-style="{
        'text-align': 'center',
        'font-size': '13px',
        color: '#333C48',
      }" stripe border element-loading-text="拼命加载中" :max-height="tableHeight" class="productList" style="width: 100%;">
        <el-table-column type="index" width="80" label="序号"></el-table-column>
        <el-table-column prop="orderNumber" label="订单编号"></el-table-column>
        <el-table-column prop="time" label="日期"></el-table-column>
        <el-table-column prop="pledgSource" label="押桶来源"></el-table-column>
        <el-table-column prop="pledgType" label="押桶方式"></el-table-column>
        <el-table-column prop="product" label="押桶信息">
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row.product" :key="index">
              {{ item.name }} ￥{{ item.money }} x {{ item.num }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="buckList" label="退桶信息">
          <template slot-scope="scope">
            <div v-for="(item, index) in scope.row.buckList" :key="index">
              {{ item.name }} ￥{{ item.money }} x {{ item.num }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="replaceBuckList" label="杂牌抵扣信息">
          <template slot-scope="scope">
            <div v-for="(it, index) in scope.row.replaceBuckList" :key="index">
              <div>
                {{ it.replaceName }}
                <span>{{ it.replaceNumber }}个</span>
              </div>
              <div>
                <span>抵扣</span>
                <span>
                  <span>{{ it.r3 }}</span>
                  {{ it.buckNumber ? it.buckNumber : it.replaceNumber }}个
                </span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="联系方式">
        </el-table-column>
        <el-table-column prop="address" label="下单地址">
        </el-table-column>
        <el-table-column prop="deliveryName" label="送水员信息">
          <template slot-scope="scope">
            <span v-if="scope.row.deliveryId == '-100'" class="color-green">水站自送</span>
            <template v-else>
              <div>{{ scope.row.deliveryName }}</div>
              <div>{{ scope.row.deliveryPhone }}</div>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="newState" label="状态">
          <template slot-scope="scope">
            <div v-if="scope.row.newState == '已退款'">已退款(线下)</div>
            <div v-else-if="scope.row.newState == '已同意'">退款成功</div>
            <div v-else>{{ scope.row.newState }}</div>
          </template>

        </el-table-column>
        <el-table-column prop="refundTime" label="退款时间"></el-table-column>
        <el-table-column prop label="操作">
          <template slot-scope="scope">
            <el-button type="text" v-if="scope.row.newState != '已回收'
              && scope.row.newState != '已退款'
              && scope.row.newState != '已同意'
              && scope.row.newState != '回收中'" @click="makeSureReturnBucketRecord1(scope.row, 1)">直接退款</el-button>
            <el-button type="text" v-if="scope.row.operation == '指派'"
              @click="openFormalDriverListDebt(scope.row)">指派</el-button>
            <el-button type="text" v-if="scope.row.operation == '同意或者拒绝'"
              @click="makeSureReturnBucketRecord(scope.row, 1)">同意</el-button>
            <!-- <el-button type="text" v-if="scope.row.operation == '同意或者拒绝'"
                  @click="makeSureReturnBucketRecord(scope.row, 2)">拒绝</el-button> -->
            <el-button type="text" v-if="scope.row.operation == '确认退款'"
              @click="makeSureReturnBucketRecord(scope.row, 1)">确认退款</el-button>
            <el-button type="text" v-if="scope.row.newState == '回收中' &&
              scope.row.deliveryId == -100
            " @click="confirmHs(scope.row)">确认回收</el-button>
            <el-button type="text" v-if="scope.row.newState != '已回收'
              && scope.row.newState != '已退款'
              && scope.row.newState != '已同意'
              && scope.row.newState != '回收中'
            " @click="adminback(scope.row.orderNumber)">拒绝</el-button>

          </template>
        </el-table-column>
      </el-table>
      <div style="position: fixed;  width: 100%;bottom: 0;" class="flex align-items-center justify-content-center">

        <el-pagination background layout="prev, pager, next" style="padding:15px" :current-page="page"
          @current-change="clickPage2" :total="orderSum">
        </el-pagination>
      </div>
    </div>
    <!-- 水票记录表 -->
    <div v-loading="isLoading" v-else-if="tapMenuValue == 11">
      <div>
        <!-- <div style="display:inline-block;margin-right:20px;">
          <el-date-picker v-model="orderSelect.date" style="width:250px;" value-format="yyyy-MM-dd" type="daterange"
            align="right" unlink-panels range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期"
            :picker-options="dateSelect">
          </el-date-picker>
        </div> -->
        <el-input v-model="isKey" placeholder="输入订单号/手机号/联系方式搜索" style="width:282px;margin-right:20px;"></el-input>
        <el-input v-model="isKey2" placeholder="输入地址搜索" style="width:282px;margin-right:20px;"></el-input>

        <el-button type="primary" @click="load()">查询</el-button>

        <el-button style="margin-left:20px;" @click="clearSearch">清空筛选条件</el-button>
      </div>
      <el-table :data="ticketList" key="pledBucket2" :header-cell-style="{
        'text-align': 'center',
        'background-color': '#EFF2F7',
      }" :cell-style="{
        'text-align': 'center',
        'font-size': '13px',
        color: '#333C48',
      }" stripe border v-loading="fuckLoading5" element-loading-text="拼命加载中" :max-height="tableHeight"
        class="productList" style="width: 100%;">
        <el-table-column type="index" width="80" label="序号"></el-table-column>
        <el-table-column prop="orderNum" label="订单编号"></el-table-column>
        <el-table-column prop="createTime" label="日期"></el-table-column>
        <el-table-column prop="userName" label="用户名"></el-table-column>
        <el-table-column prop="userMobile" label="手机号"></el-table-column>
        <el-table-column prop="address" label="用户地址">
        </el-table-column>
        <el-table-column prop="waterName" label="水票名称"></el-table-column>
        <el-table-column prop="payNum" label="水票数量"></el-table-column>
        <el-table-column prop="waterTotalPrice" label="水票总金额"></el-table-column>
        <el-table-column prop="payMent" label="摘要"></el-table-column>
        <el-table-column prop="remarks" label="备注"></el-table-column>

        <el-table-column prop="giveawayDesc" label="赠品"></el-table-column>

        <el-table-column prop="orderNumber" label="补差价">
          <template slot-scope="scope">
            <span v-if="scope.row.makePrice">￥{{ scope.row.makePrice }}</span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop label="操作" width="200">
          <template slot-scope="scope">
            <!-- <el-button type="text">确认到账</el-button>
                <el-button type="text" disabled>已到账</el-button>-->
            <el-button type="text" v-if="scope.row.orderId" @click="lookOrderDetail(scope.row)">查看详情</el-button>
            <el-button type="text" v-if="scope.row.payMent == '银行转账购买' && scope.row.affirm == 0
            " @click="makeSureGetMoney2(scope.row)">确认到账</el-button>
            <el-button type="text" v-if="scope.row.payMent == '线下付款购买' && scope.row.affirm == 0 && scope.row.userConfirm == 1
            " style="color: red">待用户确认</el-button>
            <el-button type="text" v-if="scope.row.payMent == '线下付款购买' && scope.row.affirm == 0 && scope.row.userConfirm == 2
            " @click="makeSureGetMoney4(scope.row)">确认到账</el-button>
            <el-button type="text" v-if="scope.row.payMent == '线下付款购买' && scope.row.userConfirm != 1 && scope.row.wcDetailsState == 0 && scope.row.affirm != 1
            " @click="dongjieshuipiao(scope.row, 1)">冻结</el-button>
            <el-button type="text" v-if="scope.row.payMent == '线下付款购买' && scope.row.userConfirm != 1 && scope.row.wcDetailsState == 1 && scope.row.affirm != 1
            " @click="dongjieshuipiao(scope.row, 0)" style="color: green">解除冻结</el-button>
            <el-button type="text" v-if="scope.row.payMent == '线下付款购买' && (scope.row.userConfirm != 1 && scope.row.wcDetailsState == 1) || (scope.row.affirm == 0 && scope.row.userConfirm == 1)
            " @click="shanchushuipiao(scope.row.reId, scope.row.wcDetailsId)" style="color: red">删除</el-button>
            <template v-if="scope.row.payMent == '线下付款购买,水票失效' &&
              scope.row.affirm == 0 &&
              scope.row.refund != 2
            ">
              <el-button type="text" @click="makeSureGetMoney3(scope.row)">确认未到账</el-button>
              <el-button type="text" @click="makeSureGetMoney2(scope.row)">确认到账</el-button>
            </template>
            <template v-if="scope.row.payMent == '银行转账购买,水票失效' &&
              scope.row.affirm == 0 &&
              scope.row.refund != 2
            ">
              <el-button type="text" @click="makeSureGetMoney3(scope.row)">确认未到账</el-button>
              <el-button type="text" @click="makeSureGetMoney2(scope.row)">确认到账</el-button>
            </template>
            <el-button type="text" disabled v-if="(scope.row.affirm == 1 || scope.row.payMent == '微信购买') && !scope.row.refund
            ">已到账</el-button>
            <el-button type="text" disabled v-if="scope.row.refund == 2">未到账</el-button>
            <el-button type="text" v-if="scope.row.payMent == '微信购买' && scope.row.refund == 0"
              @click="confirmRefund(scope.row, 1)">同意退款</el-button>
            <el-button type="text" v-if="scope.row.payMent == '微信购买' && scope.row.refund == 0"
              @click="confirmRefund(scope.row, 0)">拒绝退款</el-button>
            <el-button type="text" disabled v-if="scope.row.payMent == '微信购买' && scope.row.refund == 1">已退款</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="position: fixed;  width: 100%;bottom: 0;" class="flex align-items-center justify-content-center">

        <el-pagination background layout="prev, pager, next" style="padding:15px" :current-page="page"
          @current-change="clickPage3" :total="orderSum">
        </el-pagination>
      </div>
    </div>
    <div v-loading="isLoading" v-else>
      <div v-if="tapMenuValue == 3 || tapMenuValue == 4 || tapMenuValue == 12" style="display: inline-block; margin-right: 10px;">
        <!-- 全选复选框 -->
        <el-checkbox v-model="checked" @change="handleCheckAllChange">全选</el-checkbox>
      </div>
      <div :style="'width:100%;overflow-y:scroll;max-height:' + (tableHeight - 50) + 'px'
        ">
        <div class="article" v-for="(item, index) in list" :key="index">
          <!-- 头部 -->
          <div class="article_top">

            <div v-if="tapMenuValue == 3 || tapMenuValue == 4 || tapMenuValue == 12" style="display: inline-block; margin-right: 10px;">
              <!-- 单个订单复选框 -->
              <el-checkbox v-model="item.isChecked" @change="(val) => handleCheckChange(val, item)"></el-checkbox>
            </div>
                            <img v-if="item.orderSourceImage" style="width: 40px;height: 40px;"
                                :src="item.orderSourceImage"
                                alt="">
            <div v-if="item.mark" class="mark-tag">{{ item.mark }}</div>
            <span style="margin-right:20px;">订单编号：{{ item.orderNum }}</span>
            <span style="margin-right:20px;">下单时间：{{ item.orderDate }}</span>
            <span style="margin-right:20px;font-weight: 600;" v-if="item.yuyuetime">预约时间：{{ item.yuyuetime }}</span>
            <span style="margin-right:20px;">共<span class="color-red">{{ item.totalPieces }}</span>件商品</span>
            <span style="margin-right:20px;">商品实际金额：{{ item.productTotalPrice }}元</span>
            <span style="margin-right:20px;" v-if="item.buckPrice != '0.00' && item.buckPrice != '0'">押桶金合计：{{
              item.buckPrice }}元</span>
            <!-- <span style="margin-right:20px;"
                  v-if="item.upPrice!='0.00'">客户付上楼费：{{item.upPrice}}元</span> -->
            <span style="margin-right:20px;" class="color-red">订单总金额：{{ item.orderMoney }}元</span>
            <span style="margin-right:20px;" v-if="item.waterDeduct != '0.00'" class="color-red">水票抵扣：{{
              item.waterDeduct
            }}元</span>
            <!-- <span style="margin-right:50px;">{{item.payment}} {{item.total}}元</span> -->
            <span style="margin-right:20px;" v-if="item.ticketPrice != '0.00'" class="color-red">优惠券金额：{{
              item.ticketPrice
            }}元</span>
            <span style="margin-right:20px;" v-if="item.yunfei != '0.00' && item.ziti != 0" class="color-red">配送费：{{
              item.yunfei
            }}元</span>
            <span style="margin-right:20px;" v-if="item.dikou != null && item.dikou > 0" class="color-red">返现金：{{
              item.dikou
            }}元</span>
            <span style="margin-right:20px;" class="color-red">订单实付金额：{{ item.total }}元</span>
            <span style="margin-right:20px;" class="color-red">订单支付方式：{{ item.payment }}</span>
            <span style="margin-right:20px;font-weight: 600" v-if="item.pledgetCount" class="color-red">押：{{
              item.pledgetCount }}</span>
            <span style="margin-right:20px;font-weight: 600" v-if="item.debtCount" class="color-red">欠：{{ item.debtCount
            }}</span>
            <span v-if="item.isprint && item.isprint == 1"
              style="margin-right:20px;font-weight: 600;color: #00a88b">已打印</span>
            <span class=" cursor-pointer" @click="exception(item)"
              :style="item.exception ? 'margin-right:20px;font-weight: 600;color: #9933ff' : 'margin-right:20px;font-weight: 600;color: #1694FD'">{{
                item.exception ? ('标记异常原因：' + item.exception) : "暂无异常(点击修改)" }}</span>
          </div>
          <div class="article_content">
            <div
              style="min-width:394px;width:20%;padding-top:0px;padding-bottom:50px;border-left:1px solid #1694FD;display:block"
              class="box-sizing imgBox position-relative">
              <div class="flex align-items-center padding-bottom-10" style="margin-left:-20px;width:100%;height:38px;">
                <div style="padding:10px;background-color:#1694FD" class="margin-right-10 color-white"
                  v-if="item.storeUpdate == 1">
                  水站修改
                </div>
                <div style="padding:10px;background-color:#F56C6D" class="margin-right-10 color-white"
                  v-if="item.deliveryUpdateName">
                  {{ item.deliveryUpdateName }}修改
                </div>
                <div style="padding:10px 20px;background:linear-gradient(45deg, #DC322B, white);"
                  class="margin-right-10 color-white" v-if="item.buckState == 1">
                  送水员已发起押桶
                </div>
                <div v-if="item.isSend === 2" class="margin-right-10 color-white urge-icon">催单中</div>
              </div>

              <!-- 押桶申请中标志 -->
              <!-- buckState 0 不显示   1显示 -->
              <!-- 商家标志 -->
              <!-- 商品信息---------------------------------- -->
              <div style="width:100%;height:100%;">
                <div v-for="(it, idx) in item.specList" :key="idx">
                  <div class="flex  box-sizing margin-bottom-20" v-if="idx <= item.specFlag">
                    <div class="font-size-0 padding-right-20 box-sizing" style="min-width:110px;width:30%;">
                      <img :src="it.pic" alt="" style="width:90px;height:90px;" />
                    </div>
                    <div style="min-width:240px;width:70%">
                      <div class=" font-size-16">
                        {{ it.goodsName }}
                      </div>
                      <div style="margin-top:15px;">
                        <span class="color-red margin-right-20">￥{{ it.price ? it.price : it.doller }}</span>
                        <s class="margin-right-20 color-grey" v-if="it.price">￥{{ it.doller }}</s>
                        <span class="color-red">x{{ it.standardNumber }}</span>
                      </div>
                      <div>
                        <p v-for="(t, i) in it.shopGroupList" :key="i"
                          class="flex align-items-center justify-content-between">
                          <span>{{ t.goodsName }}</span>
                          <span class="color-red">x{{ t.standardNumber }}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- @click="lookAllGoods(index)" -->
              <template v-if="item.specList.length > 2">
                <div class="color-blue position-absolute cursor-pointer text-align-center"
                  style="bottom:10px;left:0;right:0;margin:0 auto;" @click="lookAllGoods(index, 0)"
                  v-if="item.specFlag < item.specList.length">
                  点击查看所有产品<i class="el-icon-arrow-down"></i>
                </div>
                <div class="color-blue position-absolute cursor-pointer" style="bottom:10px;"
                  @click="lookAllGoods(index, 1)" v-else>
                  收起<i class="el-icon-arrow-up"></i>
                </div>
              </template>
            </div>
            <!-- 客户信息  begin---------------------------------- -->
            <div style="min-width:394px;width:20%" class="flex align-items-center justify-content-center">
              <div class="article_content_two">
                <div>客户姓名：{{ item.name }}</div>
                <div style="display: flex;align-items: center;">
                  <div>客户电话：{{ item.phone }}</div>
                  <!-- 加一个刷新电话按钮 -->
                  <!-- 深蓝色 -->
                  <!-- 复制电话按钮 -->
                  <el-button type="text" style="margin-left: 5px;" size="mini"
                    @click="copyToClipboard(item.phone)" title="复制电话">
                    复制
                  </el-button>
                  <el-button type="text" style="margin-left: 10px;"
                    size="mini" @click="refreshPhone(item.orderId)">刷新电话</el-button>
                </div>
                <div>登录手机号：{{ item.userPhone }}</div>
                <div>客户类型：{{ item.companyName ? "公司" : "家庭" }}</div>
                <div v-if="item.companyName">
                  公司名称：{{ item.companyName }}
                </div>
                <div style="display: flex;align-items: center;">
                  <div>客户地址：{{ item.underway }}</div>
                  <!-- 复制地址按钮 -->
                  <el-button type="text" style="margin-left: 10px;" size="mini"
                    @click="copyToClipboard(item.underway)" title="复制地址">复制
                  </el-button>
                </div>
                <div class="color-red">
                  客户留言：{{ item.userLeaveWord ? item.userLeaveWord : "-" }}
                </div>
              </div>
            </div>
            <!-- 客户信息  end---------------------------------- -->
            <!-- 资产管理信息  begin--------------- -->
            <div style="width:217px;width:15%">
              <div v-if="tapMenuValue == 8 || item.orderStatus == 8">
                <div class="flex color-red" v-if="item.returnName">
                  <div style="width:113px;text-align:right;">申请类型：</div>
                  <div>{{ item.returnName }}</div>
                </div>
                <div class="flex color-red" v-if="item.returnContent">
                  <div style="width:113px;text-align:right;">申请原因：</div>
                  <div>{{ item.returnContent }}</div>
                </div>
                <div class="flex color-red" v-if="item.returnReason">
                  <div style="width:113px;text-align:right;">申请描述：</div>
                  <div>{{ item.returnReason }}</div>
                </div>
              </div>
              <div v-else>
                <div class="flex" v-if="item.waterUse">
                  <div style="width:113px;text-align:right">已使用水票数：</div>
                  <div>{{ item.waterUse ? item.waterUse : 0 }}</div>
                </div>
                <div class="flex" v-if="item.waterUnuse">
                  <div style="width:113px;text-align:right">未使用水票数：</div>
                  <div>{{ item.waterUnuse ? item.waterUnuse : 0 }}</div>
                </div>
                <div class="flex" v-if="item.residueGoods">
                  <div style="width:113px;text-align:right">借物未还总数：</div>
                  <div>{{ item.residueGoods }}</div>
                </div>
                <div class="flex" v-if="item.serviceOrder">
                  <div style="width:113px;text-align:right">清洗服务次数：</div>
                  <div>{{ item.serviceOrder }}</div>
                </div>
                <br />
                <!-- <el-popover placement="right" popper-class="tooltip-background" :visible-arrow="false" width="250"
                  trigger="click">
                  <div class="flex" v-if="AssetsList.thisBuck">
                    <div>本次回桶数：</div>
                    <div>{{ AssetsList.thisBuck }}</div>
                  </div>
                  <div v-if="AssetsList.lastBuck > 0">
                    上次欠桶数：{{ AssetsList.lastBuck }}
                  </div>
                  <div v-if="AssetsList.lastOwnBuckDetails.length">
                    <div class="flex align-items-center wrap">
                      （
                      <span v-for="(it, idx) in AssetsList.lastOwnBuckDetails" :key="idx" style="margin: 2.5px 5px;">
                        {{ it.name }} </span>）
                    </div>
                  </div>
                  <div v-if="AssetsList.residue > 0" style="marign-top:5px;">
                    汇总欠桶数：{{ AssetsList.residue }}
                  </div>
                  <div v-if="AssetsList.residueBuckDetails.length">
                    <div class="flex align-items-center wrap">
                      （
                      <span v-for="(it, idx) in AssetsList.residueBuckDetails" :key="idx" style="margin:2.5px 5px;">
                        {{ it.name }} </span>）
                    </div>
                  </div>
                  <div v-if="AssetsList.totalPledgeBuck > 0" style="marign-top:5px;">
                    汇总押桶数：{{ AssetsList.totalPledgeBuck }}
                  </div>
                  <div v-if="AssetsList.totalPledgeBuckPrice > 0">
                    （<span>押桶金合计：￥{{ AssetsList.totalPledgeBuckPrice }}</span>）
                  </div>

                  <div style="line-height:26px;" slot="reference">
                    <el-button type="primary" @click="lookZCinfo(item)">
                      查 看 桶 信 息
                    </el-button>
                  </div>
                </el-popover> -->
              </div>
              <el-button type="primary" @click="sendMsgMethod(item.id)">
                发送消息
              </el-button>
              <el-button type="primary" @click="sendMsgMethod2(item.id)">
                发送短信
              </el-button>
              <el-button type="primary" @click="editUser(index, item)">
                编辑客户
              </el-button>
              <el-button type="primary" @click="lookDetail(item)">
                查看客户
              </el-button>
            </div>
            <!-- 资产管理信息  end--------------- -->
            <!-- 订单状态 begin ------------------ -->
            <div style="width:217px;width:15%">
              <div>
                <div v-if="item.ziti != 0">
                  <span v-if="item.orderStatus == 1">待接单</span>
                  <span v-if="item.orderStatus == 2 && item.state == 0">待发单</span>
                  <span v-if="item.orderStatus == 2 && item.state == 1">待接单</span>
                  <div style="color: red;font-weight: bold;font-size: 24px"
                    v-if="item.orderStatus == 2 && item.back == 1">
                    退单<br />
                    {{ item.exception }}
                  </div>
                  <div style="color: red;font-weight: bold;font-size: 24px" v-if="item.daike && item.daike == 1">
                    {{ item.userLeaveWord }}
                  </div>
                  <div style="color: blue;font-weight: bold;font-size: 20px" v-if="item.isYaTong && item.isYaTong == 1">
                    该订单已完成押桶
                  </div>

                  <div v-if="item.affirm == 1" style="width:100%;text-align:center">
                    已收款
                  </div>

                  <!-- <span v-if="item.orderStatus==2&&item.state==2 && !item.deliveryId">待发货</span> -->
                  <span v-if="item.orderStatus == 2 && item.state == 2">待发货</span>
                  <span v-if="item.orderStatus == 3 && item.state != 3">已发货</span>
                </div>
                <div v-else>自提</div>
                <span v-if="item.orderStatus == 5">已签收</span>
                <span v-if="item.orderStatus == 10">已完成</span>
                <span v-if="item.orderStatus == 9">已评价</span>
                <span v-if="item.orderStatus == 6">已取消</span>
                <span v-if="item.orderStatus == 7">已拒绝</span>
                <span v-if="item.orderStatus == 8 && item.returnState == 1">{{ item.returnName }}申请中</span>
                <span v-if="item.orderStatus == 8 && item.returnState == 2">{{ item.returnName }}已同意</span>
                <span v-if="item.orderStatus == 8 && item.returnState == 3">{{ item.returnName }}已拒绝</span>
                <br />
                <el-button @click="orderwuliudetail(item)" type="text"
                  v-if="item.orderStatus > 2 && item.orderStatus != 8 && item.deliveryId == -1 && item.ziti != 0">查看物流</el-button>
              </div>
            </div>
            <!-- 订单状态 begin ------------------ -->
            <!-- 送水员  begin ---------------- -->
            <div style="width:217px;width:15%">
              <div style="line-height:26px;">
                <div v-if="item.deliveryName">
                  姓名：{{ item.deliveryName }}
                </div>
                <div v-if="item.deliveryPhone">
                  电话：{{ item.deliveryPhone }}
                </div>
                <div v-if="item.distributionTime">
                  指派时间：{{ item.distributionTime }}
                </div>
                <div class="color-red margin-top-10" v-if="item.totalMoeny != '0' && item.totalMoeny != '0.00'">
                  配送提成合计:￥{{ item.totalMoeny }}
                </div>
                <div v-if="item.shopMoney != '0' && item.shopMoney != '0.00'">
                  配送单件提成:￥{{ item.shopMoney }}/件
                </div>
                <div v-if="item.deliveryMoney != '0' && item.deliveryMoney != '0.00'
                ">
                  配送业务提成:￥{{ item.deliveryMoney }}/件
                </div>
                <div v-if="item.distanceMoney != '0' && item.distanceMoney != '0.00'
                ">
                  配送距离提成:￥{{ item.distanceMoney }}/件
                </div>
                <div v-if="item.floorMonety != '0' && item.floorMonety != '0.00'">
                  水站付上楼费:￥{{ item.floorMonety }}/件
                </div>
                <div v-if="item.upPrice != '0' && item.upPrice != '0.00'">
                  客户付上楼费:￥{{ item.upPrice }}/件
                </div>
              </div>
            </div>
            <!-- 送水员  end ---------------- -->
            <div style="width:217px;width:15%;border-right:none;">
              <div class="operate color-blue text-align-center" style="line-height:26px;">
                <!-- <div
                  v-if="item.orderStatus == 2 && item.state == 0"
                  @click="chooseFormalDriver(item)"
                >
                  水站自送
                </div> -->
                <div v-if="item.orderStatus == 2 && item.state == 0 && item.ziti != 0"
                  @click="chooseFormalDriver(item)">
                  固定送水员
                </div>
                <div @click="openOrderLocation(item)">
                  查看位置
                </div>
                <div v-if="item.orderStatus == 2 && item.state == 0 && item.ziti != 0" @click="wuliu(item)">
                  物流配送
                </div>
                <div v-if="item.orderStatus == 2 && item.state == 0 && item.ziti == 0" @click="ziti(item)">
                  自提核销
                </div>
                <div v-if="item.orderStatus == 1" @click="
                  updateOrderState({
                    orderId: item.orderId,
                    orderStatus: '2',
                    state: '0',
                    type: '接单',
                  })
                  ">
                  替代送水员接单
                </div>


                <template v-if="item.deliveryName &&
                  item.orderStatus == 3 &&
                  item.state == 2
                ">
                  <!-- <div @click="
                    replaceOrder({ order: item.orderId, id: item.deliveryId })
                    ">
                    代接单
                  </div> -->
                  <div v-if="item.deliveryStatus == 0" @click="
                    cancelOrder({ orderId: item.orderId })
                    ">
                    撤销派单
                  </div>
                  <div v-if="item.deliveryStatus == 0" @click="
                    changeDelivery(item)
                    ">
                    改派单
                  </div>
                  <div v-if="item.deliveryStatus == 0" @click="
                    orderConfirm({ orderId: item.orderId })
                    ">
                    确认送达
                  </div>
                </template>
                <div v-if="item.orderStatus < 5" @click="orderrefund(item.orderId)">
                  直接退款
                </div>
                <div
                  v-if="item.orderStatus == 2 && item.state == 0 && item.ziti != 0 && item.ordersource != null && item.ordersource != 0"
                  @click="orderbackadmin(item)">
                  回退订单
                </div>
                <div @click="printHandler(item)">
                  打印
                </div>
                <div @click="printSfExpress(item)" v-if="(adminStoreInfo.role1 == 4 || adminStoreInfo.storeId == 1841) && item.orderStatus == 2 && item.state == 0 && item.ziti != 0" style="color: #67C23A;">
                  打印顺丰单号
                </div>
                <div @click="urgeStore(item)" style="color: #9c27b0;">
                  催单发货
                </div>
                <div @click="showOrderLog(item.orderNum)" class="order-log-btn">
                  操作记录
                </div>
                <div v-if="getPicUrlArray(item.picurl).length > 0" class="pic-container">
                  <img v-for="(pic, index) in getPicUrlArray(item.picurl).slice(0, 3)" :key="index" :src="pic"
                    class="pic-thumbnail" @click="previewImages(getPicUrlArray(item.picurl), index)"
                    @error="handleImageError" />
                  <span v-if="getPicUrlArray(item.picurl).length > 3" class="pic-more">
                    +{{ getPicUrlArray(item.picurl).length - 3 }}
                  </span>
                </div>

                <!-- <div v-if="item.orderStatus==2&&item.state==0 && item.isHaveSalesman == 0"
                     @click="updateOrderState({type:'临时',orderId:item.orderId,orderStatus:'2',state:'1',isAlert:item.isAlert,userId:item.id})">临时送水员</div> -->
                <div v-if="item.orderStatus == 2 &&
                  item.state == 1 &&
                  !item.deliveryName
                " @click="
                  updateOrderState({
                    type: '取消发单',
                    orderId: item.orderId,
                    orderStatus: '2',
                    state: '0',
                  })
                  ">
                  取消发单
                </div>
                <div v-if="item.orderStatus == 2 && item.state == 2 && !item.deliveryId
                " @click="
                  updateOrderState({
                    type: '发货',
                    orderId: item.orderId,
                    orderStatus: '3',
                    state: '1',
                  })
                  ">
                  发货
                </div>

                <template v-if="item.orderStatus == 8 && item.returnState == 1">
                  <div @click="dealSale(item.orderReturnsId, 1)">同意</div>
                  <div @click="dealSale(item.orderReturnsId, 2)">拒绝</div>
                </template>
                <!-- <div @click.stop="goSetDeduct(item)" v-if="item.isAlert">
                  设置提成
                </div> -->
                <!-- <div @click.stop="goSetDeduct(item)" v-else>
                  编辑客户/修改提成
                </div> -->

                <div v-if="item.buckState == 1" @click.stop="cancelBucket(item)">
                  撤销押桶
                </div>
                <div @click.stop="makeSureGetMoney(item)" v-if="item.payment == '银行转账' && item.affirm == 0">
                  确认收款
                </div>
                <div @click.stop="makeSureGetMoney(item)" v-if="item.payment == '线下付款' && item.affirm == 0">
                  确认收款
                </div>

                <!-- <div @click="goOrderDetail(item)"
                     v-if="item.orderStatus<3 && item.state<3">修改订单</div> -->
                <!-- <div @click="lookUpOperateLog(item)">操作日志</div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div style="position: fixed;  width: 100%;bottom: 0;" class="flex align-items-center justify-content-center">
        <el-pagination background layout="prev, pager, next" style="padding:15px" :current-page="page"
          @current-change="clickPage" :total="orderSum">
        </el-pagination>
      </div>
    </div>

    <!-- 弹窗 -->
    <!-- 新增手填订单 -->
    <el-dialog :title="handOrderForm.orderNum ? '编辑手填订单' : '新增手填订单'" :visible.sync="handOrderDialog" width="35%"
      :before-close="handleClose">
      <div>
        <el-form ref="handOrderElement" :model="handOrderForm" :rules="handOrderRules" label-width="120px">
          <el-form-item label="用户姓名" prop="name">
            <el-input v-model="handOrderForm.name" placeholder="请输入用户姓名"></el-input>
          </el-form-item>
          <el-form-item label="手机号码" prop="phone">
            <el-input v-model="handOrderForm.phone" placeholder="请输入手机号码"></el-input>
          </el-form-item>
          <!-- <el-form-item label="所在地区">
            <div class="flex align-items-center justify-content-between cursor-pointer"
                 @click="getLocation"><span>{{handOrderForm.addressMap}}</span><i class="el-icon-arrow-right"></i></div>
          </el-form-item> -->
          <el-form-item label="所在地区" prop="addressMap">
            <el-input placeholder="请选择地址" ref="mapInput" :focus="mapFocus" :value="handOrderForm.addressMap">
              <template slot="append">
                <div @click="getLocation">
                  地图选址 <i class="el-icon-arrow-right"></i>
                </div>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item label="详细地址" prop="address">
            <el-input v-model="handOrderForm.address" placeholder="请输入详细地址"></el-input>
          </el-form-item>
          <el-form-item label="是否为电梯房" prop="isLift">
            <el-switch v-model="handOrderForm.isLift"></el-switch>
          </el-form-item>
          <el-form-item label="是否为公司地址" prop="isCompanyAddress">
            <el-switch v-model="handOrderForm.isCompanyAddress"></el-switch>
          </el-form-item>
          <el-form-item label="公司地址" prop="companyName" v-if="handOrderForm.isCompanyAddress">
            <el-input v-model="handOrderForm.companyName" placeholder="请输入公司名称"></el-input>
          </el-form-item>
          <el-form-item label="选择楼层" prop="floor">
            <el-select v-model="handOrderForm.floor" placeholder="请选择楼层">
              <el-option v-for="(item, index) in handOrderSelectOptions" :label="item.label" :value="item.value"
                :key="index"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="配送商品"
                        prop="flag">
            <el-radio-group v-model="handOrderForm.flag">
              <el-radio label="0">自定义</el-radio>
              <el-radio label="1">选择商品</el-radio>
            </el-radio-group>
          </el-form-item> -->
          <div v-if="handOrderForm.flag == '0'">
            <div v-for="(item, index) in handOrderForm.customGoods" :key="index">
              <el-form-item>
                <div>第{{ index + 1 }}件商品</div>
              </el-form-item>
              <el-form-item :label="'商品名称' + (index + 1)" :prop="'customGoods.' + index + '.name'" :rules="{
                required: true,
                message: '商品名称不能为空',
                trigger: 'blur',
              }">
                <el-input v-model="item.name" placeholder="请输入商品名称"></el-input>
              </el-form-item>
              <el-form-item :label="'商品数量' + (index + 1)" :prop="'customGoods.' + index + '.num'" :rules="{
                required: true,
                message: '商品数量不能为空',
                trigger: 'blur',
              }">
                <el-input v-model.number="item.num" placeholder="请输入商品数量" @input="countSingleTotal(index)"></el-input>
              </el-form-item>
              <el-form-item :label="'商品单价' + (index + 1)" :prop="'customGoods.' + index + '.price'" :rules="{
                required: true,
                message: '商品单价不能为空',
                trigger: 'blur',
              }">
                <el-input v-model.number="item.price" placeholder="请输入商品单价" @input="countSingleTotal(index)"></el-input>
              </el-form-item>
              <el-form-item :label="'商品总价'" :prop="'customGoods.' + index + '.total'">
                <div class="color-blue">￥{{ item.total }}</div>
              </el-form-item>
            </div>
            <el-form-item class="flex align-items-center justify-content-center">
              <el-button type="primary" @click="addCustomGoods">新增商品</el-button>
              <el-button type="danger" @click="delCustomGoods">删除商品</el-button>
            </el-form-item>
          </div>
          <div v-else>
            <el-form-item label="选择商品">
              <div class="flex align-items-center justify-content-between cursor-pointer" @click="chooseGoods">
                <div></div>
                <div>
                  <span class="color-grey margin-right-30">请选择商品</span><i class="el-icon-arrow-right"></i>
                </div>
              </div>
            </el-form-item>
            <el-form-item prop="sendGoods">
              <div class="goodsCard flex box-shadow" v-for="(item, index) in handOrderForm.sendGoods.checkList"
                :key="index">
                <div class="flex align-items-center">
                  <el-image style="width: 80px; height: 80px" :src="item.content" fit="fit"></el-image>
                </div>
                <div>
                  <div>{{ item.spuName }}</div>
                  <div>
                    <el-tag type="success" v-if="item.skuName">{{
                      item.skuName
                    }}</el-tag>
                  </div>
                  <div class="flex align-items-center justify-content-between">
                    <div class="color-red font-size-16">
                      <span class="font-size-12">￥</span>{{ item.newPrice }}
                    </div>
                    <div>x{{ item.num }}</div>
                  </div>
                </div>
              </div>
            </el-form-item>
          </div>
          <el-form-item label="商品件数">
            <div class="color-blue">
              {{
                handOrderForm.flag == 1
                  ? countHandOrderCheckList
                  : handOrderForm.customGoods.length
              }}
            </div>
          </el-form-item>
          <el-form-item label="订单总额">
            <div class="color-blue">
              {{ handOrderForm.flag == 1 ? countSendTotal : countCustomTotal }}
            </div>
          </el-form-item>

          <div v-if="handOrderForm.orderNum">
            <el-form-item label="支付方式" prop="payName">
              <div class="color-blue">{{ handOrderForm.payName }}</div>
            </el-form-item>
            <el-form-item label="订单编号" prop="orderNum">
              <div class="color-blue">{{ handOrderForm.orderNum }}</div>
            </el-form-item>
            <el-form-item label="订单时间" prop="orderTime">
              <div class="color-blue">{{ handOrderForm.orderTime }}</div>
            </el-form-item>
          </div>
        </el-form>
      </div>
      <!-- 商品 -->
      <el-dialog width="50%" title="选择商品" :visible.sync="sendGoodsDialog" append-to-body>
        <div>
          <div class="flex align-items-center">
            <el-input v-model="handOrderForm.sendGoods.searchKey" placeholder="请输入商品或规格名称" clearable
              @clear="chooseGoods" style="width:200px;margin-right:10px;"></el-input>
            <el-button type="primary" @click="toSearchProduct">搜索</el-button>
          </div>
          <el-table :data="handOrderForm.sendGoods.list" :header-cell-style="{ 'text-align': 'center' }" :cell-style="{
            'text-align': 'center',
            'font-size': '13px',
            color: '#333C48',
          }" @select="sendGoodsCheck" :max-height="inTableHeight" ref="sendGoodsElement" class="productList"
            style="width:100%">
            <el-table-column type="selection" width="55"> </el-table-column>
            <el-table-column prop="" label="商品图" width="100">
              <template slot-scope="scope">
                <img :src="scope.row.content" alt="" style="width:75px;height:75px;" />
              </template>
            </el-table-column>
            <el-table-column prop="spuName" label="名称"> </el-table-column>
            <el-table-column prop="skuName" label="规格"> </el-table-column>
            <el-table-column prop="newPrice" label="价格"> </el-table-column>
            <el-table-column prop="inventory" label="库存"> </el-table-column>
            <el-table-column prop="num" width="150" label="数量">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.num" :min="1" label="输入数量"></el-input-number>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="sendGoodsDialog = false">取 消</el-button>
          <el-button type="primary" @click="sureSendGoods">确 定</el-button>
        </div>
      </el-dialog>
      <!-- 地图 -->
      <el-dialog width="500px" title="地图" :visible.sync="mapDialog" append-to-body>
        <div>
          <iframe id="mapPage" width="100%" height="600px" frameborder="0"
            src="https://apis.map.qq.com/tools/locpicker?search=1&type=1&key=NM5BZ-UTYHP-PZ2DF-VESJ4-T6AQ3-7KFOH&referer=web&mapdraggable=0">
          </iframe>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="mapSure">确 定</el-button>
        </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handOrderDialog = false">取 消</el-button>
        <el-button type="primary" @click="handOrderSubmit">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 修改订单 -->
    <el-dialog title="" :visible.sync="orderDetailDialog" class="detailDialog" width="600px">
      <div class="text-align-center font-size-30 padding-tb-20 border-bottom">
        修改订单
      </div>
      <div style="background-color:#F5F5F5" class="text-align-center font-size-16 padding-tb-10 margin-tb-20">
        买家信息
      </div>
      <el-form ref="editOrderElement" :model="orderDetailData" label-width="110px">
        <el-form-item label="客户类型">
          <el-radio-group v-model="orderDetailData.userType">
            <el-radio :label="1">公司用户</el-radio>
            <el-radio :label="0">家庭用户</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="flex align-items-center justify-content-between margin-bottom-10">
          <el-form-item label="联系人" prop="name">
            <el-input v-model="orderDetailData.userName" style="width:100px;" placeholder="请输入姓名"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="orderDetailData.userPhone" placeholder="请输入手机号" style="width:120px;"
              oninput="value=value.replace(/[^\d]/g,'')"></el-input>
          </el-form-item>
        </div>
        <el-form-item label="地址" prop="" class="address-box">
          <el-cascader v-model="orderDetailData.region" placeholder="试试搜索：上海" :options="areaOptions" :props="props"
            filterable clearable></el-cascader>
          <el-input v-model="orderDetailData.detailedAddress" placeholder="请输入详细地址具体到小区名称" style="margin-top:12px"
            v-if="orderDetailData.accountIndex == 0"></el-input>
          <el-input v-model="orderDetailData.detailedAddress" placeholder="请输入详细地址具体到园区名称" style="margin-top:12px"
            v-else></el-input>
        </el-form-item>
        <div class="flex align-items-center justify-content-between margin-top-10">
          <div class="flex align-items-center justify-content-between">
            <el-form-item label="是否是电梯房">
              <el-switch v-model="orderDetailData.islift"></el-switch>
              <span class="margin-left-10">{{
                orderDetailData.isLift ? "是" : "否"
              }}</span>
            </el-form-item>
            <el-form-item label="选择上楼数">
              <el-select v-model="orderDetailData.floor" placeholder="请选择楼层数">
                <el-option v-for="item in handOrderSelectOptions" :key="item.value" :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
          </div>
        </div>
        <el-form-item label="买家留言">
          <div class="color-red">
            {{
              orderDetailData.userLeaveWord == null
                ? "买家没有留言哦~"
                : orderDetailData.userLeaveWord
            }}
          </div>
        </el-form-item>
        <div style="background-color:#F5F5F5" class="text-align-center font-size-16 padding-tb-10 margin-tb-20">
          商品信息
        </div>

        <div class="" style="background-color:#F5F5F5;padding:0 20px;">
          <!-- 普通商品 -->
          <div v-if="orderDetailData.specList">
            <div class="flex align-items-center justify-content-between"
              v-for="(item, index) in orderDetailData.specList" :key="index">
              <div class="goodsCard flex" style="padding:15px;flex:1">
                <div class="flex align-items-center">
                  <el-image style="width: 80px; height: 80px" :src="item.pic" fit="fit"></el-image>
                </div>
                <div>
                  <div class="flex">
                    <span style="width:80px;">商品名称：</span>
                    <span style="flex:1">{{ item.goodsName }}{{ item.specName }}</span>
                  </div>
                  <div class="padding-tb-10 flex">
                    <span style="width:80px;">商品价格：</span>
                    <span style="flex:1">￥{{ item.price ? item.price : item.doller }}</span>
                  </div>
                  <div class="flex align-items-center">
                    <span style="width:80px;">商品数量：</span>
                    <div>
                      <el-input-number :min="1" :max="item.maxNum" v-model="item.standardNumber"
                        label="请输入数量"></el-input-number>
                    </div>
                  </div>
                </div>
              </div>
              <!-- <div class="flex align-items-center justify-content-center"
                 style="width:100px;">
              <i class="el-icon-delete color-grey"></i>
              <span class="color-grey margin-left-10">删除</span>
            </div> -->
            </div>
          </div>
          <!-- 组合商品 -->
          <div v-if="orderDetailData.shopGroupList">
            <div v-for="(item, index) in orderDetailData.shopGroupList" :key="index">
              <div class="flex align-items-center justify-content-between">
                <div style="flex:1">
                  <div class="goodsCard flex" style="padding:15px;">
                    <div class="flex align-items-center">
                      <el-image style="width: 80px; height: 80px" :src="item.pic" fit="fit"></el-image>
                    </div>
                    <div>
                      <div class="flex">
                        <span style="width:80px;">商品名称：</span>
                        <span style="flex:1">{{ item.goodsName }}</span>
                      </div>
                      <div class="flex padding-tb-10">
                        <span style="width:80px;">商品价格：</span>
                        <span style="flex:1">￥{{ item.price ? item.price : item.doller }}</span>
                      </div>
                      <div class="flex align-items-center">
                        <span style="width:80px;">商品数量：</span>
                        <div>
                          <el-input-number :min="1" :max="item.maxNum" v-model="item.standardNumber"
                            label="请输入数量"></el-input-number>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-for="(it, idx) in item.specList" :key="idx"
                    class="flex align-items-center justify-content-between padding-lr-10">
                    <div class="padding-bottom-10">{{ it.goodsName }}</div>
                    <div class="color-grey">x{{ it.standardNumber }}</div>
                  </div>
                </div>
                <!-- <div class="flex align-items-center justify-content-center"
                     style="width:100px;">
                  <i class="el-icon-delete color-grey"></i>
                  <span class="color-grey margin-left-10">删除</span>
                </div> -->
              </div>
            </div>
          </div>
        </div>
        <div style="padding:20px 30px;">
          <div class="flex align-items-center justify-content-between margin-bottom-10">
            <div style="width:40%">
              <!-- 订单总金额：<span style="color:#E6A23C">{{orderDetailData.footing}}元</span> -->
              订单总金额：<span style="color:#E6A23C">{{ countEditOrderMoney }}元</span>
            </div>
            <div style="width:60%">
              订单编号：{{ orderDetailData.orderNumber }}
            </div>
          </div>
          <div class="flex align-items-center justify-content-between margin-bottom-10">
            <div style="width:40%">
              客户付上楼费：{{ orderDetailData.upPrice }}元
            </div>
            <div style="width:60%">
              下单时间：{{ orderDetailData.orderDate }}
            </div>
          </div>
          <div class="flex align-items-center justify-content-between margin-bottom-10">
            <div style="width:40%">
              水票抵扣：{{ orderDetailData.orderDiscounts }}元
            </div>
            <div style="width:60%">
              商品数量：共{{ orderDetailData.totalPieces }}件商品
            </div>
          </div>
          <!-- <div>
            线下付款：7.00
          </div> -->
        </div>
        <div style="background-color:#F5F5F5" class="text-align-center font-size-16 padding-tb-10 margin-bottom-20">
          提成信息
        </div>
        <div class="padding-lr-30">
          配送提成：<span style="color:#E6A23C">{{
            orderDetailData.totalMoeny ? orderDetailData.totalMoeny : "0.00"
          }}元</span>
        </div>
        <div class="flex margin-top-10 padding-lr-30">
          <div>明细：</div>
          <div>
            <div class="flex align-items-center justify-content-between margin-bottom-10">
              <div style="width:200px">
                单件提成：{{
                  orderDetailData.shopMoney
                    ? orderDetailData.shopMoney
                    : "0.00"
                }}元
              </div>
              <div style="width:200px">
                距离提成：{{
                  orderDetailData.distanceMoney
                    ? orderDetailData.distanceMoney
                    : "0.00"
                }}元
              </div>
            </div>
            <div class="flex align-items-center justify-content-between">
              <div style="width:200px">
                水站付上楼费：{{
                  orderDetailData.floorMonety
                    ? orderDetailData.floorMonety
                    : "0.00"
                }}元
              </div>
              <div style="width:200px">
                业务提成：{{
                  orderDetailData.deliveryMoney
                    ? orderDetailData.deliveryMoney
                    : "0.00"
                }}元
              </div>
            </div>
          </div>
        </div>
        <div style="background-color:#F5F5F5" class="text-align-center font-size-16 padding-tb-10 margin-tb-20">
          修改备注
        </div>
        <div style="padding:0 30px;">
          <!-- id是无用字段，利用上 -->
          <el-input type="textarea" :rows="3" v-model="orderDetailData.id" placeholder="在此处输入修改备注~">
          </el-input>
        </div>
        <div style="padding:10px 0px;">
          <el-divider class="bold">待接单</el-divider>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="orderDetailDialog = false">取 消</el-button>
        <el-button type="primary" @click="editOrderMakeSure">保 存</el-button>
      </div>
    </el-dialog>

    <!-- 固定送水员选择 -->
    <el-dialog title="选择派送方式" :visible.sync="formalDriverDialog" width="50%">
      <div>
        <!-- 添加水站自送选项 -->
        <div style="margin-bottom: 20px; padding: 15px; border: 1px solid #e4e7ed; border-radius: 4px;">
          <el-radio v-model="deliveryType" label="self" @change="selectDeliveryType('self')">
            <span style="font-weight: 500; color: #409EFF;">水站自送</span>
          </el-radio>
          <div style="margin-top: 8px; font-size: 12px; color: #909399; margin-left: 20px;">
            选择水站自送，订单将标记为自送状态
          </div>
        </div>

        <!-- 送水员选择 -->
        <div style="margin-bottom: 15px;">
          <el-radio v-model="deliveryType" label="driver" @change="selectDeliveryType('driver')">
            <span style="font-weight: 500;">选择送水员</span>
          </el-radio>
        </div>

        <el-table :data="formalDriverList" :header-cell-style="{ 'text-align': 'center' }" :cell-style="{
          'text-align': 'center',
          'font-size': '13px',
          color: '#333C48',
        }" style="width: 100%" :max-height="inTableHeight" :class="{ 'disabled-table': deliveryType === 'self' }">
          <el-table-column label="选择" width="55" align="center" label-class-name="radio-selected">
            <template slot-scope="scope">
              <el-radio v-model="thisFormalDriver" :label="scope.row" :disabled="deliveryType === 'self'">&nbsp;</el-radio>
            </template>
          </el-table-column>

          <el-table-column prop="imgUri" label="主图" width="100" style="height:500px;">
            <template slot-scope="scope">
              <img :src="scope.row.imgUrl
                ? scope.row.imgUrl
                : imgUri + '/images/driverLogo.jpg'
                " alt="" style="width:75px;height:75px;" />
            </template>
          </el-table-column>
          <el-table-column prop="name" label="姓名">
            <template slot-scope="scope">
              {{ scope.row.name ? scope.row.name : "派送员" }}
            </template>
          </el-table-column>
          <el-table-column prop="phone" label="联系方式"> </el-table-column>
          <el-table-column prop="remarks" label="配送范围"> </el-table-column>
          <el-table-column prop="" label="身份状态">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.shortTime == 1">固定</el-tag>
              <el-tag type="warning" v-else>临时</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="" label="配送状态">
            <template slot-scope="scope">
              <span class="color-grey" v-if="scope.row.delectState == 1">离职</span>
              <span class="color-red" v-else-if="scope.row.delivery == 1">配送中...</span>
              <span class="color-green" v-else>空闲</span>
            </template>
          </el-table-column>
          <el-table-column prop="" label="派送中">
            <template slot-scope="scope">
              {{ scope.row.currentTaskSum }}单
            </template>
          </el-table-column>
          <el-table-column prop="" label="已完成">
            <template slot-scope="scope">
              {{ scope.row.total }}单
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="formalDriverDialog = false">取 消</el-button>
        <el-button type="primary" @click="formalDriverSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 操作日志 -->
    <el-dialog title="" :visible.sync="operateData.dialog" width="900px">
      <div class="text-align-center font-size-30 padding-tb-20 border-bottom">
        操作日志
      </div>
      <div class="padding-tb-20">订单编号：{{ operateData.orderNum }}</div>
      <el-table :data="operateData.list" key="operateLog" :header-cell-style="{
        'text-align': 'center',
        'background-color': '#EFF2F7',
      }" :cell-style="{
        'text-align': 'center',
        'font-size': '13px',
        color: '#333C48',
      }" stripe border :max-height="inTableHeight">
        <el-table-column prop="userRole" label="操作用户">
          <template slot-scope="scope">
            <span>{{ scope.row.userRole == 1 ? "送水员" : "水站人员" }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="userAction" label="执行动作"> </el-table-column>
        <el-table-column prop="updateBefore" label="修改前值">
        </el-table-column>
        <el-table-column prop="updateLater" label="修改后值"> </el-table-column>
        <el-table-column prop="createTime" label="发生时间"> </el-table-column>
        <el-table-column prop="userRemark" label="修改备注"> </el-table-column>
      </el-table>
    </el-dialog>
    <!-- 新增编辑客户 -->
    <el-dialog title :visible.sync="addUserModal" width="700px">
      <div class="text-align-center font-size-30 padding-tb-20 border-bottom">
        {{ userFormData.userId ? "编辑客户" : "新增客户" }}
      </div>
      <div style="background-color: #f5f5f5;" class="text-align-center font-size-16 padding-tb-10 margin-tb-20">
        基本信息
      </div>
      <el-form ref="addUserElement" :model="userFormData" :rules="userFormDataRules" label-width="110px">
        <el-form-item label="客户类型">
          <el-radio-group v-model="userFormData.accountIndex">
            <el-radio :label="1">公司用户</el-radio>
            <el-radio :label="0">家庭用户</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="el-form-flex">
          <el-form-item label="联系人" prop="name">
            <el-input v-model="userFormData.name" style="width: 100px;" placeholder="请输入姓名"></el-input>
          </el-form-item>
          <el-form-item label="客户备注" prop="nickName">
            <el-input v-model="userFormData.nickName" style="width: 100px;" placeholder="请输入备注"></el-input>
          </el-form-item>
          <el-form-item label="手机号" prop="phone">
            <el-input v-model="userFormData.phone" placeholder="请输入手机号" style="width: 120px;"
              oninput="value=value.replace(/[^\d]/g,'')"></el-input>
          </el-form-item>
        </div>
        <template v-if="userFormData.accountIndex == 1">
          <el-form-item label="公司名称" prop="companyName">
            <el-input v-model="userFormData.companyName" placeholder="请输入公司名称"></el-input>
            <div style="color: red;font-weight: 600">填写公司名称便于公司和商家沟通信息通畅！</div>
          </el-form-item>
        </template>
        <el-form-item label="地址" prop class="address-box">
          <el-cascader v-model="userFormData.region" placeholder="试试搜索：上海" :options="areaOptions" :props="props"
            filterable clearable></el-cascader>
          <el-input v-model="userFormData.address" placeholder="请输入详细地址具体到小区名称" style="margin-top: 12px;"
            v-if="userFormData.accountIndex == 0"></el-input>
          <el-input v-model="userFormData.address" placeholder="请输入详细地址具体到园区名称" style="margin-top: 12px;"
            v-else></el-input>
        </el-form-item>
        <div style="background-color: #f5f5f5;" class="text-align-center font-size-16 padding-tb-10 margin-tb-20">
          提成信息
        </div>
        <div>
          <div class="flex align-items-center justify-content-between">
            <el-form-item label="是否是电梯房">
              <el-switch v-model="userFormData.isLift"></el-switch>
              <span class="margin-left-10">
                {{ userFormData.isLift ? "是" : "否" }}
              </span>
            </el-form-item>
            <el-form-item label="选择上楼数">
              <el-select v-model="userFormData.floorNum" placeholder="请选择楼层数">
                <el-option v-for="item in floorOptions" :key="item.value" :label="item.label"
                  :value="item.value"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div class="flex align-items-center justify-content-between">
            <el-form-item label="距离提成" prop="distanceTc">
              <el-input v-model="userFormData.distanceTc" oninput="value=value.replace(/[^\d.]/g,'')"
                placeholder="请填写距离提成"></el-input>
            </el-form-item>
          </div>
        </div>

        <div v-if="userFormData.lastGoodsList != false" style="background-color: #f5f5f5;"
          class="padding-lr-20 padding-bottom-20">
          <div class="color-blue padding-tb-20">
            上次商品消费 可作为提成设置、优惠设置参考
          </div>
          <el-table :data="userFormData.lastGoodsList" :header-cell-style="{
            'text-align': 'center',
            'background-color': '#EFF2F7',
          }" border style="width: 100%;">
            <el-table-column prop="goodsName" label="商品名称" align="center"></el-table-column>
            <el-table-column prop="doller" label="零售价格" align="center"></el-table-column>
            <el-table-column prop="standardNumber" label="购买数量" align="center">
              <template slot-scope="scope">
                <span class="color-blue">{{ scope.row.standardNumber }}</span>
              </template>
            </el-table-column>
            <el-table-column label="是否设置优惠" align="center">
              <template slot-scope="scope">
                <span v-if="scope.row.discounts == '已设置'" class="color-green">{{ scope.row.discounts }}</span>
                <span v-else-if="scope.row.discounts == '未设置'" class="color-red">{{ scope.row.discounts }}</span>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div style="background-color: #f5f5f5;" class="text-align-center font-size-16 padding-tb-10 margin-tb-20">
          优惠设置
        </div>
        <div class="overflow-hidden cursor-pointer" style="padding: 10px 0 20px 0;" v-if="userFormData.userId"
          @click="setDiscount(userFormData.userId)">
          <div class="color-blue text-align-right" style="line-height: 14px;">
            <el-badge :value="userFormData.discountGoodsList.length" class="item">
              <span>优惠设置</span>
            </el-badge>
            <span class="padding-left-30">>></span>
          </div>
        </div>
        <div v-if="userFormData.discountGoodsList != false" class="margin-bottom-20">
          <el-table :data="userFormData.discountGoodsList" border :header-cell-style="{
            'text-align': 'center',
            'background-color': '#EFF2F7',
          }" :cell-style="{
            'text-align': 'center',
            'font-size': '13px',
            color: '#333C48',
          }" style="width: 100%;">
            <el-table-column prop="productName" label="商品名称"></el-table-column>
            <el-table-column prop="retail" label="零售价格"></el-table-column>
            <el-table-column prop="price" label="客户优惠价格"></el-table-column>
            <el-table-column prop="buy" label="客户水票买赠优惠规则">
              <template slot-scope="scope">
                <span v-if="scope.row.buy == '不赠'" class="color-red">
                  {{ scope.row.buy }}
                </span>
                <span v-else-if="scope.row.buy == '-'">
                  {{ scope.row.buy }}
                </span>
                <span v-else class="color-green">{{ scope.row.buy }}</span>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <div v-for="(item, index) in userFormData.switchList" :key="index">
          <div v-if="item.moduleName == '借物功能' || item.moduleName == '月付功能'">
            <div v-if="index == 0">
              <el-form-item :label="item.moduleName" :prop="item.alias">
                <div class="flex align-items-center justify-content-between">
                  <div class="flex align-items-center">
                    <el-switch v-model="item.state" :active-value="1" :inactive-value="0"></el-switch>
                    <span class="font-size-14 color-red margin-left-10">
                      {{ powerListContent[index].content }}
                    </span>
                  </div>
                  <div v-if="item.state == 1" class="flex align-items-center">
                    <span class="margin-right-10">清洗费用</span>
                    <el-input v-model="item.servePrice" oninput="value=value.replace(/[^\d.]/g,'')"
                      :placeholder="'请输入' + item.moduleName + '用'" style="width: 100px;"></el-input>
                  </div>
                  <div v-else></div>
                </div>
              </el-form-item>
            </div>
            <!-- switchJw -->
            <div v-if="index > 0 && index < userFormData.switchList.length">
              <el-form-item :label="item.moduleName" :prop="item.alias">
                <div class="flex align-items-center">
                  <el-switch v-model="item.state" @change="stateSwitch($event, item)" :active-value="1"
                    :inactive-value="0"></el-switch>
                  <span class="font-size-14 color-red margin-left-10">
                    {{ powerListContent[index].content }}
                  </span>
                </div>
              </el-form-item>
            </div>
          </div>
        </div>
        <el-form-item label="消费提醒" class="labelBlue" prop="isOpen" v-if="moduleList.xftx == 1">
          <div class="flex  justify-content-between">
            <el-switch v-model="userFormData.isOpen"></el-switch>
            <span class="color-red font-size-14 margin-left-10">打开后，水站可设置客户的下单周期。周期内的时间客户没下单，会提醒水站要催促客户下单</span>
          </div>
        </el-form-item>
        <el-form-item label="提醒间隔" prop="day" v-if="moduleList.xftx == 1">
          <el-input v-model="userFormData.day" oninput="value=value.replace(/[^\d]/g,'')"
            placeholder="请输入提醒间隔，单位天"></el-input>
        </el-form-item>
      </el-form>
      <!-- 内层dialog开始 -->
      <el-dialog width="1000px" title="商品优惠设置" :visible.sync="discountDialog" :before-close="closeRemoveDialog"
        append-to-body>
        <div style="margin-bottom: 20px;">
          <el-radio-group v-model="discountData.classifyName" size="small">
            <el-radio-button v-for="(item, index) in discountData.classifyList" :key="index"
              :label="item.name"></el-radio-button>
          </el-radio-group>
        </div>
        <div class="set-office">
          <p>
            1、【客户优惠价格】即为客户设置的专属商品优惠价格，即一个客户一个商品价格
            <span style="
                text-decoration: underline;
                color: rgba(22, 147, 253, 1);
                cursor: pointer;
                margin-left: 30px;
              " v-if="!textShow" @click="textShow = true">展示全部</span>
          </p>
          <template v-if="textShow">
            <p>2、不设置【客户优惠价格】，则无法设置【客户水票买赠优惠规则】</p>
            <p>
              3、设置【客户优惠价格】后，该客户购买水票的价格等于【客户优惠价格】
            </p>
            <p>
              4、【客户优惠价格】、【客户水票买赠优惠规则】是针对单个客户进行的设置。
              <span style="
                  text-decoration: underline;
                  color: rgba(22, 147, 253, 1);
                  cursor: pointer;
                  margin-left: 30px;
                " @click="textShow = false">收起</span>
            </p>
          </template>
        </div>
        <el-table :data="discountData.list" :max-height="inTableHeight" ref="discountElement" style="width: 100%;">
          <el-table-column prop label="商品图" width="100">
            <template slot-scope="scope">
              <img :src="scope.row.imgUrl" alt style="width: 75px; height: 75px;" />
            </template>
          </el-table-column>
          <el-table-column prop="spuName" label="名称" width="150"></el-table-column>
          <el-table-column prop="details" label="规格"></el-table-column>
          <el-table-column prop="cost" label="成本价格"></el-table-column>
          <el-table-column prop="retail" label="零售价格"></el-table-column>
          <el-table-column prop label="设置商品优惠价格" width="300">
            <template slot-scope="scope">

              <div style="display: flex;justify-content: space-between;align-items: center;height: 88px;">
                <div @click="changeIndex(scope.$index, 0)"
                  :class="0 == scope.row.chooseListIndex ? 'check123' : 'nocheck'">
                  商品优惠
                </div>
                <div @click="changeIndex(scope.$index, 1)" v-if="scope.row.isWater && scope.row.isWater == 1"
                  :class="1 == scope.row.chooseListIndex ? 'check123' : 'nocheck'">
                  水票优惠(无赠送)
                </div>
                <div @click="changeIndex(scope.$index, 2)" v-if="scope.row.isWater && scope.row.isWater == 1"
                  :class="2 == scope.row.chooseListIndex ? 'check123' : 'nocheck'">
                  水票优惠(有赠送)
                </div>
              </div>

              <div v-if="scope.row.chooseListIndex == 0" class="font-size-28 bold flex align-items-center margin-top-10"
                style="border: 1px solid #000000;padding: 5px;border-radius: 5px;justify-content: space-between;">
                <div class="font-size-28 bold flex align-items-center margin-top-10">
                  <div style="font-size: 12px;width: 50px">单买产品优惠价格：</div>
                  <input class="inputBox margin-left-10" style="width:66px;font-size: 14px;font-weight: 500;"
                    v-model="scope.row.userPrice" type="digit" placeholder="请输入" />
                  元
                </div>
              </div>
              <div v-if="scope.row.chooseListIndex == 1" class="font-size-28 bold  margin-top-10"
                style="border: 1px solid #000000;padding: 5px;border-radius: 5px;justify-content: space-between;">
                <div class="flex align-items-center  margin-top-30" style="justify-content: space-between;">
                  <div class="flex align-items-center">

                    <div style="font-size: 12px;width: 50px">固定买水票</div>
                    <input class="inputBox margin-left-10" style="width:66px;font-size: 14px;font-weight: 500;"
                      v-model="scope.row.buy" type="number" placeholder="请输入" />

                    <div style="font-size: 12px;width: 50px">张，优惠价格：</div>
                    <input class="inputBox margin-left-10" v-model="scope.row.userPrice"
                      style="width:66px;font-size: 14px;font-weight: 500;" type="digit" placeholder="请输入" />
                    <div style="font-size: 12px;">元</div>
                  </div>
                </div>
              </div>
              <div v-if="scope.row.chooseListIndex == 2" class="font-size-28 bold  margin-top-10"
                style="border: 1px solid #000000;padding: 5px;border-radius: 5px;justify-content: space-between;">
                <div class="flex align-items-center  margin-top-30" style="justify-content: space-between;">
                  <div class="flex align-items-center">

                    <div style="font-size: 12px;width: 50px">水票优惠价格：</div>
                    <input class="inputBox margin-left-10" style="width:66px;font-size: 14px;font-weight: 500;"
                      v-model="scope.row.userPrice" type="number" placeholder="请输入" />

                  </div>
                </div>
                <div class="flex align-items-center  margin-top-30" style="justify-content: space-between;">
                  <div class="flex align-items-center">
                    <div style="font-size: 12px;width: 50px">水票买赠优惠：买</div>
                    <input class="inputBox margin-left-10" style="width:66px;font-size: 14px;font-weight: 500;"
                      v-model="scope.row.buy" type="number" placeholder="请输入" />
                    <div>赠</div>
                    <input class="inputBox margin-left-10" style="width:66px;font-size: 14px;font-weight: 500;"
                      v-model="scope.row.send" type="number" placeholder="请输入" />
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label align="center">
            <template slot-scope="scope">
              <el-button type="primary" @click="makeSureDiscount(scope.row)">确认</el-button>
            </template>
          </el-table-column>
          <!-- 其他移除优惠设置 begin -->
          <el-table-column label align="center" v-if="discountData.classifyId != 1 && discountData.classifyId != 0">
            <template slot-scope="scope">
              <el-button type="danger" plain @click.stop="otherRemoveUprice(scope.row)"
                v-if="scope.row.classId != 1 && scope.row.userPrice">移除</el-button>
            </template>
          </el-table-column>

          <el-table-column label align="center" v-if="discountData.classifyId == 1 || discountData.classifyId == 0">
            <template slot-scope="scope">
              <el-button type="danger" plain @click="openRemoveDialog(scope.row)"
                v-if="scope.row.classId == 1 && scope.row.userPrice">移除</el-button>
              <span v-else-if="scope.row.classId == 1 && !scope.row.userPrice"></span>
              <span v-else-if="scope.row.classId != 1">-</span>
            </template>
          </el-table-column>
        </el-table>
      </el-dialog>
      <!-- 内层dialog结束 -->
      <el-dialog title :visible.sync="removeDialog" width="30%" class="removeClass" append-to-body>
        <div>
          <div class="padding">
            <el-radio v-model="removeWhich" label="0">移除水票买赠规则</el-radio>
          </div>
          <div class="padding">
            <el-radio v-model="removeWhich" label="1">移除优惠价格与水票买赠规则</el-radio>
          </div>
          <div class="padding color-red">
            注：移除水票买赠规则：只会删除水票买赠规则；移除优惠价格与水票买赠规则：同时把客户优惠价格和水票买赠规则同时删除
          </div>
        </div>
        <div slot="footer" class="flex align-items-center justify-content-between">
          <div></div>
          <div>
            <el-button @click="
              removeDialog = false
            removeWhich = '0'
              ">取 消</el-button>
            <el-button type="primary" @click="makeSureToRemove">确 定</el-button>
          </div>
        </div>
      </el-dialog>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addUserModal = false">取 消</el-button>
        <el-button type="primary" @click="addUserSubmit">确 定</el-button>
      </div>
    </el-dialog>

    <el-dialog :visible.sync="newDemandMsgVisible" width="36%" center :before-close="handleClose" class="new-dialog-css"
      custom-class="msg-dia-log-css">
      <div slot="title" class="new-dialog-title">发消息</div>
      <div class="new-dialog-two-title">模板选择</div>
      <div class="new-dialog-body">
        <el-form ref="msgform" :model="newDemandForm" label-width="155px" class="new-msg-b">
          <el-form-item prop="msgType">
            <el-radio-group v-model="newDemandForm.newMsgType">
              <el-radio :label="0">通知</el-radio>
              <el-radio :label="1">活动</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择消息模板：" class="tem-msg" v-if="msgTempList != false">
            <el-radio-group v-model="newDemandForm.msgTemplate" size="medium">
              <template v-for="(item, index) in msgTempList">
                <el-radio border :label="item.templateId" :key="index" v-if="5 > index">{{ item.templateName
                }}</el-radio>
              </template>
              <el-button type="text" @click="newMsgInnder = true">查看更多<i class="el-icon-d-arrow-right"></i></el-button>
            </el-radio-group>
          </el-form-item>
          <div class="new-dialog-two-title" style="margin-bottom: 20px;">
            发消息
          </div>
          <el-form-item label="消息名称：">
            <el-input placeholder="请输入消息名称（最多6个字）" v-model="newDemandForm.msgTitle" maxlength="6"></el-input>
          </el-form-item>
          <el-form-item prop="msgContent" label="消息内容：">
            <el-input type="textarea" placeholder="请输入消息内容" v-model="newDemandForm.msgContent"
              :autosize="{ minRows: 5, maxRows: 10 }"></el-input>
          </el-form-item>
          <el-form-item label="是否保存为消息模板：">
            <el-switch v-model="newDemandForm.saveMsgTem"> </el-switch>
          </el-form-item>
          <el-form-item :label="showUserListType == 1 ? '已选客户：' : '已选客户数：'">
            <span>
              <template v-if="showUserListType == 1">
                <span>{{ choiceUserList }}</span>
              </template>
              <template v-else-if="showUserListType == 2">
                <span>{{ userIDList.length }}</span>
              </template>
              <template v-else>
                <span>已选择全部客户</span>
              </template>
            </span>
          </el-form-item>
          <el-form-item label="选择时间：" v-if="newDemandForm.newMsgType == 1">
            <el-col :span="11">
              <el-date-picker v-model="newDemandForm.startTime" @change="changeEndTime(1)"
                value-format="yyyy-MM-dd HH:mm:ss" type="datetime" :picker-options="msgDateDisable" placeholder="开始时间">
              </el-date-picker>
            </el-col>
            <el-col class="line" :span="2">-</el-col>
            <el-col :span="11">
              <el-date-picker v-model="newDemandForm.endTime" @change="changeEndTime(1)"
                value-format="yyyy-MM-dd HH:mm:ss" type="datetime" :picker-options="msgDateDisable" placeholder="结束时间">
              </el-date-picker>
            </el-col>
          </el-form-item>
          <div class="color-red">
            开始时间，结束时间即为消息在消费者端展示的起止时间，若消费者不屏蔽此消息，时间范围内每天展示一次。
          </div>
        </el-form>
      </div>
      <!-- The second floor dialog -->
      <el-dialog width="50%" title="消息模板" :visible.sync="newMsgInnder" append-to-body center>
        <el-table :data="msgTempList" max-height="400px" :header-cell-style="{
          'text-align': 'center',
          'background-color': '#F5F5F5',
        }" :cell-style="{
          'text-align': 'center',
          'font-size': '13px',
          color: '#333C48',
        }">
          <el-table-column label="选择" width="100">
            <template slot-scope="scope">
              <el-radio v-model="newDemandForm.msgTemplate" :label="scope.row.templateId">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <el-table-column prop="templateName" label="消息名称">
          </el-table-column>
          <el-table-column prop="templateContent" label="消息内容">
          </el-table-column>
          <el-table-column label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click.stop="editMsgTempalte(scope.row)">编辑</el-button>
              <el-button type="text" size="medium" style="color: red;"
                @click.stop="deleteMsgTemApi(scope.row.templateId, 1)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button @click="DeselectTem">取 消</el-button>
          <el-button type="primary" @click="deleSelection">确定选择</el-button>
        </span>
        <!-- 第三层 模板编辑 -->
        <el-dialog width="40%" title="消息模板" :visible.sync="newMsgThreeInnder" append-to-body center
          class="new-dialog-css">
          <div slot="title" class="new-dialog-title">消息模板</div>
          <div class="new-dialog-two-title">编辑模板</div>
          <el-form label-width="100px" class="new-msg-b" style="margin-top: 20px;">
            <el-form-item label="消息名称：">
              <el-input placeholder="请输入消息名称（最多6个字）" v-model="editMsgTemForm.editMsgTitle" maxlength="6"></el-input>
            </el-form-item>
            <el-form-item prop="msgContent" label="消息内容：">
              <el-input type="textarea" placeholder="请输入消息内容" v-model="editMsgTemForm.editMsgCont"
                :autosize="{ minRows: 5, maxRows: 10 }"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="newMsgThreeInnder = false">取 消</el-button>
            <el-button type="primary" @click="saveEditMsgTem">保 存</el-button>
          </span>
        </el-dialog>
      </el-dialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelSendMsg">取 消</el-button>
        <el-button type="primary" @click="sendNewMsgGo">发 送</el-button>
      </span>
    </el-dialog>
    <!-- 发消息 New demand for sand sculpture end -->
    <!-- 发短信 begin -->
    <el-dialog :visible.sync="newDemandMsgVisible2" width="36%" center :before-close="handleClose"
      class="new-dialog-css" custom-class="msg-dia-log-css">
      <div slot="title" class="new-dialog-title">发短信</div>
      <div class="new-dialog-two-title">模板选择</div>
      <div class="new-dialog-body">
        <el-form ref="msgform" :model="newDemandForm2" label-width="155px" class="new-msg-b">
          <el-form-item prop="msgType">
            <el-radio-group v-model="newDemandForm2.newMsgType">
              <el-radio :label="4">通知</el-radio>
              <el-radio :label="5">活动</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择短信模板：" class="tem-msg" v-if="msgTempList2 != false">
            <el-radio-group v-model="newDemandForm2.msgTemplate" size="medium">
              <template v-for="(item, index) in msgTempList2">
                <el-radio border :label="item.templateId" :key="index" v-if="5 > index">{{ item.templateName
                }}</el-radio>
              </template>
              <el-button type="text" @click="newMsgInnder2 = true">查看更多<i class="el-icon-d-arrow-right"></i></el-button>
            </el-radio-group>
          </el-form-item>
          <div class="new-dialog-two-title" style="margin-bottom: 20px;">
            发短信
          </div>
          <el-form-item label="短信名称：">
            <el-input placeholder="请输入短信名称（最多6个字）" v-model="newDemandForm2.msgTitle" maxlength="6"></el-input>
          </el-form-item>
          <el-form-item prop="msgContent" label="短信内容：">
            <el-input type="textarea" placeholder="请输入短信内容" v-model="newDemandForm2.msgContent"
              :autosize="{ minRows: 5, maxRows: 10 }"></el-input>
          </el-form-item>
          <el-form-item label="是否保存为短信模板：">
            <el-switch v-model="newDemandForm2.saveMsgTem"> </el-switch>
          </el-form-item>
          <el-form-item :label="showUserListType2 == 1 ? '已选客户：' : '已选客户数'">
            <span>
              <template v-if="showUserListType2 == 1">
                <span>{{ choiceUserList2 }}</span>
              </template>
              <template v-else-if="showUserListType2 == 2">
                <span>{{ userIDList.length }}</span>
              </template>
              <template v-else>
                <span>已选择全部客户</span>
              </template>
            </span>
          </el-form-item>
        </el-form>
      </div>
      <!-- The second floor dialog -->
      <el-dialog width="50%" title="短信模板" :visible.sync="newMsgInnder2" append-to-body center>
        <el-table :data="msgTempList2" max-height="500px" :header-cell-style="{
          'text-align': 'center',
          'background-color': '#F5F5F5',
        }" :cell-style="{
          'text-align': 'center',
          'font-size': '13px',
          color: '#333C48',
        }">
          <el-table-column label="选择" width="100">
            <template slot-scope="scope">
              <el-radio v-model="newDemandForm2.msgTemplate" :label="scope.row.templateId">&nbsp;</el-radio>
            </template>
          </el-table-column>
          <el-table-column prop="templateName" label="消息名称" width="200">
          </el-table-column>
          <el-table-column prop="templateContent" label="消息内容">
          </el-table-column>
          <el-table-column label="操作" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="medium" @click.stop="editMsgTempalte2(scope.row)">编辑</el-button>
              <el-button type="text" size="medium" style="color: red;"
                @click.stop="deleteMsgTemApi(scope.row.templateId, 2)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <span slot="footer" class="dialog-footer">
          <el-button @click="DeselectTem2">取 消</el-button>
          <el-button type="primary" @click="deleSelection2">确定选择</el-button>
        </span>
        <!-- 第三层 模板编辑 -->
        <el-dialog width="40%" title="消息模板" :visible.sync="newMsgThreeInnder2" append-to-body center
          class="new-dialog-css">
          <div slot="title" class="new-dialog-title">消息模板</div>
          <div class="new-dialog-two-title">编辑模板</div>
          <el-form label-width="100px" class="new-msg-b" style="margin-top: 20px;">
            <el-form-item label="消息名称：">
              <el-input placeholder="请输入消息名称（最多6个字）" v-model="editMsgTemForm2.editMsgTitle" maxlength="6"></el-input>
            </el-form-item>
            <el-form-item prop="msgContent" label="消息内容：">
              <el-input type="textarea" placeholder="请输入消息内容" v-model="editMsgTemForm2.editMsgCont"
                :autosize="{ minRows: 5, maxRows: 10 }"></el-input>
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="newMsgThreeInnder2 = false">取 消</el-button>
            <el-button type="primary" @click="saveEditMsgTem2">保 存</el-button>
          </span>
        </el-dialog>
      </el-dialog>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelSendMsg2">取 消</el-button>
        <el-button type="primary" @click="sendNewMsgGo2">发 送</el-button>
      </span>
    </el-dialog>
    <!-- 发短信 end -->
    <orderwuliu v-if="orderwuliuVisible" ref="orderwuliu" @refreshDataList="load">
    </orderwuliu>
    <orderexception v-if="orderexceptionVisible" ref="orderexception" @refreshDataList="load">
    </orderexception>
    <orderbackadmin v-if="orderbackadminVisible" ref="orderbackadmin" @refreshDataList="load">
    </orderbackadmin>
    <orderziti v-if="orderzitiVisible" ref="orderziti" @refreshDataList="load">
    </orderziti>
    <orderwuliudetail v-if="orderwuliudetailVisible" ref="orderwuliudetail" @refreshDataList="load">
    </orderwuliudetail>
    <orderlocation v-model="orderlocationVisible" :longitude="longitude" :latitude="latitude" :title="locationTitle"
      width="100%" height="500px" dialog-width="70%">
    </orderlocation>
    <!-- 打印 -->
    <div style="position: absolute;bottom: 100px;left: 500px;width: 500px;display: none;">
      <div id="printBill">
        <div style="font-size: 13px; color: #000000">
          <div style="width: 100%;text-align: center;margin: 20px 0;font-size: 24px;font-weight: 600;">
            {{ JSON.parse(Cookies.get("storeInfo")).name }}
          </div>
          <div style="width: 100%;border-bottom: 1px dotted black;"></div>
          <!-- <div
            style="width: 100%;text-align: center;margin-top: 10px;font-size: 20px;"
          >
            房间入住凭据
          </div> -->
          <div style="margin-top: 20px;  width: 100%;font-size: 20px;">
            <div>订单编号：{{ indexOrder.orderNum }}</div>
            <div>下单时间：{{ indexOrder.orderDate }}</div>
            <div v-if="indexOrder.yuyuetime">预约时间：{{ indexOrder.yuyuetime }}</div>
            <div>下单人：{{ indexOrder.name }}</div>
            <!-- <div>联系方式：{{ indexOrder.phone | maskPhone }}</div> -->
            <div style="display: flex;align-items: center;">
              <div>联系方式：{{ indexOrder.phone }}</div>
              <el-button type="success" style="margin-left: 10px;" size="mini"
                @click="copyToClipboard(indexOrder.phone)" title="复制电话">
                <i class="el-icon-document-copy"></i>
              </el-button>
            </div>
            <div style="display: flex;align-items: center;">
              <div>客户地址：{{ indexOrder.underway }}</div>
              <el-button type="success" style="margin-left: 10px;" size="mini"
                @click="copyToClipboard(indexOrder.underway)" title="复制地址">
                <i class="el-icon-document-copy"></i>
              </el-button>
            </div>
            <div style="margin: 10px">
              <div v-for="item in indexOrder.specList" :key="item.id">
                <div>商品名称：{{ item.goodsName }}</div>
                <div>数量：{{ item.standardNumber }}</div>
              </div>
            </div>
            <div>支付方式：{{ indexOrder.payment }}</div>
            <div style="font-weight: 600;">备注：{{ indexOrder.userLeaveWord }}</div>
          </div>
        </div>
      </div>
    </div>

    <view @click="refreshData" class="lianying">
      <view>刷新</view>
      <view>数据</view>
    </view>

    <!-- 图片预览对话框 -->
    <el-dialog title="图片预览" :visible.sync="imagePreviewVisible" width="60%" center>
      <div class="image-preview-container">
        <el-carousel v-if="previewImageList.length > 1" :initial-index="currentImageIndex" height="400px"
          indicator-position="outside">
          <el-carousel-item v-for="(image, index) in previewImageList" :key="index">
            <img :src="image" class="preview-image" @error="handleImageError" />
          </el-carousel-item>
        </el-carousel>
        <div v-else class="single-image-container">
          <img :src="previewImageList[0]" class="preview-image" @error="handleImageError" />
        </div>
      </div>
    </el-dialog>

    <!-- 批量送达对话框 -->
    <el-dialog title="批量送达" :visible.sync="batchDeliveredVisible" width="60%" center>
      <div class="batch-delivered-container">
        <el-form :model="batchDeliveredForm" label-width="120px">
          <el-form-item label="选中订单数:">
            <span style="color: #409EFF; font-weight: bold;">{{ batchDeliveredForm.selectedOrderCount }} 个订单</span>
          </el-form-item>

          <el-form-item label="上传送达凭证:" required>
            <div class="upload-section">
              <el-upload
                class="batch-voucher-uploader"
                action="https://szmsh.waterstation.com.cn/szm/szmcordermaincontroller/uploadPic"
                :show-file-list="false"
                :data="batchUploadData"
                :on-success="handleBatchVoucherSuccess"
                :on-error="handleBatchVoucherError"
                :before-upload="beforeBatchVoucherUpload"
                :disabled="batchDeliveredForm.uploading"
                accept=".jpg,.jpeg,.png,.pdf"
                multiple>
                <el-button
                  :loading="batchDeliveredForm.uploading"
                  type="primary"
                  icon="el-icon-upload">
                  {{ batchDeliveredForm.uploading ? '上传中...' : '选择凭证文件' }}
                </el-button>
              </el-upload>
              <div class="upload-tip">
                支持 jpg、png、pdf 格式，文件大小不超过 5MB<br>
                <span style="color: #f56c6c;">注意：请为每个订单上传送达凭证，建议一次性选择多个文件</span>
              </div>
            </div>
          </el-form-item>

          <el-form-item label="已上传凭证:" v-if="batchDeliveredForm.uploadedImages.length > 0">
            <div class="uploaded-images">
              <div
                v-for="(image, index) in batchDeliveredForm.uploadedImages"
                :key="index"
                class="uploaded-image-item">
                <img
                  :src="image.url"
                  class="uploaded-image"
                  @click="previewBatchImages(batchDeliveredForm.uploadedImages.map(img => img.url), index)"
                  @error="handleImageError" />
                <div class="image-index">{{ index + 1 }}</div>
                <el-button
                  type="danger"
                  size="mini"
                  icon="el-icon-delete"
                  class="delete-image-btn"
                  @click="removeBatchImage(index)">
                </el-button>
              </div>
            </div>
            <div class="upload-count-tip">
              已上传 {{ batchDeliveredForm.uploadedImages.length }} 张凭证
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchDeliveredVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmBatchDelivered"
          :disabled="batchDeliveredForm.uploadedImages.length === 0 || batchDeliveredForm.uploading"
          :loading="batchDeliveredForm.processing">
          {{ batchDeliveredForm.processing ? '处理中...' : '确认送达' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 批量回退对话框 -->
    <el-dialog title="批量回退订单" :visible.sync="batchBackVisible" width="50%" center>
      <div class="batch-back-container">
        <el-form :model="batchBackForm" label-width="120px">
          <el-form-item label="选中订单数:">
            <span style="color: #409EFF; font-weight: bold;">{{ batchBackForm.selectedOrderCount }} 个订单</span>
          </el-form-item>

          <el-form-item label="回退原因:" required>
            <el-input
              v-model="batchBackForm.reason"
              type="textarea"
              :rows="4"
              placeholder="请输入回退原因"
              maxlength="200"
              show-word-limit>
            </el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="batchBackVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="confirmBatchBack"
          :disabled="!batchBackForm.reason.trim() || batchBackForm.processing"
          :loading="batchBackForm.processing">
          {{ batchBackForm.processing ? '处理中...' : '确认回退' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 订单操作记录弹窗 -->
    <el-dialog title="订单操作记录" :visible.sync="showOrderLogModal" width="50%" :before-close="closeOrderLogModal">
      <div v-loading="orderLogLoading" element-loading-text="加载中...">
        <div v-if="orderLogData && orderLogData.length > 0" class="order-log-content">
          <div class="log-item" v-for="(logItem, index) in orderLogData" :key="index">
            <div class="log-time">{{ logItem.createTime }}</div>
            <div class="log-content">{{ logItem.storeMsgModel }}</div>
            <div class="log-detail" v-if="logItem.content">{{ logItem.content }}</div>
          </div>
        </div>
        <div v-else-if="!orderLogLoading" class="no-log">
          <div class="no-log-text">暂无操作记录</div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeOrderLogModal">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { gcj02ToWgs84 } from '@/utils/map/coordTransform';

import print from "print-js";
import orderwuliu from './order-wuliu'
import orderexception from './order-exception'
import orderbackadmin from './order-backadmin'
import orderziti from './order-ziti'
import orderwuliudetail from './order-wuliudetail'
import orderlocation from './order-location.vue'
import { ordersource } from "@/data/common"
export default {
  props: {},
  data() {
    let that = this
    var checkPhone = (rule, value, callback) => {
      if (value) {
        let flag = that.$util.checkPhone(value)
        if (!flag) {
          callback(new Error("请输入有效的手机号码"))
        } else {
          callback()
        }
      } else {
        callback()
      }
    }
    return {
      selectedOrders: [],
      isChangeDelivery: 0,
      longitude: 116.404,
      latitude: 39.915,
      locationTitle: '位置信息',
      orderlocationVisible: false,
      // 订单操作记录相关
      showOrderLogModal: false, // 是否显示操作记录弹窗
      orderLogData: null, // 操作记录数据列表
      orderLogLoading: false, // 操作记录加载状态
      currentOrderNum: '', // 当前查看的订单号
      ordersourcefilter: ordersource,
      ordersource: '',
      indexOrder: {},
      adminStoreInfo: {}, // 管理员店铺信息
      orderzitiVisible: false,
      orderwuliuVisible: false,
      orderexceptionVisible: false,
      orderbackadminVisible: false,
      orderwuliudetailVisible: false,
      detailSelectData: {
        date: "",
        isKey: "",
        isKey2: "",
        payId: "",
        refundId: "",
        pledMoneyId: "",
        pledBucketId: "",
        remarkId: "",
        makeSureId: "",
        countStatusId: "",
        addressId: "",
        getMoneyId: ""
      },
      detailChildData: {
        tapValue: 0
      },
      finishData: {
        list: [],
        dialog: false
      },
      detailSelectOptions: {
        payType: [
          // 按回桶补差价支付方式筛选
          { name: "线下微信", value: "1" },
          { name: "线上微信", value: "2" },
          { name: "线下支付宝", value: "3" },
          { name: "现金", value: "4" }
        ],
        refundList: [
          // 按退款方式筛选
          { name: "线下微信", value: "1" },
          { name: "线上微信", value: "2" },
          { name: "现金支付", value: "3" },
          { name: "线下支付宝", value: "4" },
          { name: "线上支付宝", value: "5" },
          { name: "银行转账", value: "6" },
          { name: "线下付款", value: "7" }
        ],
        pledMoneyList: [
          // 按押桶金支付方式筛选
          { name: "线下微信", value: 7 },
          { name: "线上微信", value: 1 },
          { name: "现金支付", value: 4 },
          { name: "线下支付宝", value: 3 },
          // { name: "线上支付宝", value: "5" },
          { name: "银行转账", value: 5 },
          { name: "线下付款", value: 6 }
        ],
        pledBucket: [
          // 按押桶来源筛选
          // { name: "线下补押桶", value: "1" },
          // { name: "线上押桶", value: "2" },
          // { name: "自提押桶", value: "3" },
          // { name: "手填押桶", value: "4" }
          { name: "线上押桶", value: "0" },
          { name: "送水员押桶", value: "1" },
          { name: "水站代客押桶", value: "2" }
        ],
        remarkList: [
          // 按摘要筛选
          { name: "微信支付", value: 1 },
          { name: "银行转账", value: 2 },
          { name: "线下付款", value: 3 },
          { name: "兑换", value: 5 },
          { name: "抵扣", value: 4 }
        ],
        makeSureList: [
          // 按确认状态筛选
          { name: "已到账", value: "1" },
          { name: "未到账", value: "0" }
        ],
        countStatusList: [
          // 按结算状态筛选
          { name: "全部", value: "-1" },
          { name: "未收款", value: "0" },
          { name: "已收款", value: "1" },
          { name: "部分收款", value: "2" }
        ],
        borrowType: [
          // 按借还类型筛选
          { name: "出借", value: "1" },
          { name: "归还", value: "2" }
        ],
        getMoneyList: [
          // 按收款状态筛选
          { name: "已收款", value: "1" },
          { name: "未收款", value: "0" }
        ]
      },

      // 新增客户
      addUserModal: false,
      removeDialog: false, // 移除弹框
      removeWhich: "0",
      removeSkuId: "",
      floorOptions: [
        {
          value: 1,
          label: "一层"
        },
        {
          value: 2,
          label: "二层"
        },
        {
          value: 3,
          label: "三层"
        },
        {
          value: 4,
          label: "四层"
        },
        {
          value: 5,
          label: "五层"
        },
        {
          value: 6,
          label: "六层"
        },
        {
          value: 7,
          label: "七层及以上"
        }
      ],
      userFormData: {
        userId: 0, // 编辑时的userId
        name: "",
        nickName: "",
        phone: "",
        landLine: "",
        companyName: "",
        region: [],
        address: "",
        addressId: 0,
        isLift: 0,
        floorNum: 1,
        upFloorMoney: "",
        businessFlag: 0,
        upFloorTc: "",
        distanceTc: "",
        singleTc: "",
        businessTc: "",
        isOpen: false, // 购水提醒
        day: "",
        count: 0,
        qx: false, // 清洗服务
        jw: false, // 借物功能
        yf: false, // 月付功能
        functionFlag: 0, // 0 代表功能权限是要新增  1代表功能权限是要修改
        servicePrice: 0,
        switchList: [
          {
            alias: "qx",
            moduleName: "清洗服务",
            servePrice: 0,
            state: 0,
            userSwitchId: 0,
            content: "打开后，客户可见此功能，即可使用"
          },
          {
            alias: "jw",
            moduleName: "借物功能",
            servePrice: null,
            state: 0,
            userSwitchId: 0,
            content: "打开后，客户可见此功能，即可借物"
          },
          {
            alias: "yf",
            moduleName: "月付功能",
            servePrice: null,
            state: 0,
            userSwitchId: 0,
            content: "打开后，客户可见此功能，即可月结"
          },
          {
            alias: "yhzz",
            moduleName: "银行转账",
            servePrice: null,
            state: 0,
            userSwitchId: 0,
            content: "打开后，客户可见此功能，即可银行转账"
          },
          {
            alias: "pp",
            moduleName: "普票开关",
            servePrice: null,
            state: 0,
            userSwitchId: 0,
            content: "打开后，客户可见此功能，即可申请开普票"
          },
          {
            alias: "zp",
            moduleName: "专票开关",
            servePrice: null,
            state: 0,
            userSwitchId: 0,
            content: "打开后，客户可见此功能，即可申请开专票"
          },
          {
            alias: "gmsp",
            moduleName: "购买水票",
            servePrice: null,
            state: 1,
            userSwitchId: 0,
            content: "打开后，客户可见此功能，即可购买水票"
          },
          {
            alias: "xsyt",
            moduleName: "线上押桶",
            servePrice: null,
            state: 1,
            userSwitchId: 0,
            content: "打开后，客户可见此功能，即可线上押桶"
          },
          {
            alias: "ytjl",
            moduleName: "押桶记录",
            servePrice: null,
            state: 1,
            userSwitchId: 0,
            content: "打开后，客户可见此功能，即可线上押桶"
          }
        ],
        discountGoods: [], // 选中的商品集合
        discountGoodsList: [], // 选中的商品展示
        waterDiscounts: [],
        accountIndex: 1,
        lastGoodsList: []
      },
      powerListContent: [
        {
          content: "打开后，客户可预约清洗服务"
        },
        {
          content: "打开后，客户可发起借物资申请"
        },
        {
          content: "打开后，客户下单时可使用月结付款的支付方式"
        },
        {
          content: "打开后，客户下单时可使用银行转账的支付方式"
        },
        {
          content: "打开后，客户只能申请开普通发票"
        },
        {
          content: "打开后，客户可申请开增值税专用发票也可申请开普通发票"
        },
        {
          content: "关闭后，客户不可购买水票。（系统默认每个客户都可买水票）"
        },
        {
          content: "关闭后，客户不可进行押桶操作"
        },
        {
          content: "关闭后，客户不可查看线下押桶的记录，只可查看线上押桶的记录"
        }
      ],
      userFormDataRules: {
        name: [{ required: true, message: "姓名不能为空", trigger: "blur" }],
        // phone: [
        //   { validator: checkPhone, trigger: "change" }
        // ],
        sex: [{ required: true, message: "请选择性别", trigger: "blur" }],
        companyName: [{ required: true, message: "请输入公司名称", trigger: "blur" }],
      },
      detailLoading: true,
      // 用户详情
      userDetail: {
        userId: "",
        info: "",
        listInfo: {
          userBuckList: [],
          storeBuckList: []
        },
        currentData: "",
        formData: {
          remarkDialog: false,
          remark: ""
        },
        list: [],
        count: 0,
        page: 1,
        pageSize: 10,
        pageSizeList: [10, 15, 20]
      },
      // 物资借还记录表
      yinghuanAllnum: 0,
      yihuanAllnum: 0,
      weihuanAllnum: 0,
      // 订单列表
      orderList: [],
      orderSpecList: [],
      orderLoading: false,

      // 优惠设置的参数
      discountDialog: false,
      discountData: {
        classifyId: 1,
        classifyState: 0,
        classifyList: [],
        classifyName: "桶装水",
        list: [],
        allList: []
      },
      discountCheckList: [],

      // 水票设置的优惠参数
      waterDiscountDialog: false,
      waterDiscountData: {
        list: [],
        waterChecked: []
      },

      // 固定送水员
      formalDriverDialog: false, // 固定送水员dialog
      formalDriverList: [], // 固定送水员列表
      thisFormalDriver: "", // 选中的固定送水员
      currentOrderData: "", // 要指派的订单
      formlDriverState: "", // 0为押桶退桶指派  1为返还指派
      deliveryType: "driver", // 派送方式：driver-送水员派送，self-水站自送
      // 查看总应收款
      shouldMoney: {
        list: [],
        page: 1,
        orderSum: 0
      },
      // 模块控制
      moduleList: {},

      // ---------------新加
      // andCompanyNum: 0, // 公司用户
      // andPersonNum: 0, // 居民用户
      // allUserNum: 0, // 全部用户
      // selfNum: 0, // 自提客户

      customerNum: {
        // 客户数量
        allNum: 0, // 全部
        onLineNum: 0, // 线上
        andCompanyNum: 0, // 公司用户
        andPersonNum: 0, // 居民用户
        allUserNum: 0, // 全部用户
        selfNum: 0, // 自提客户
        noAddressNum: 0, // 无地址用户数量

        todayAddUserNum: 0,
        todayAddCompanyNum: 0,
        todayAddHomeNum: 0,
        todayAddSelfNum: 0,
        todayAddNoAddressNum: 0
      },

      userType: -1,
      userTypeList: [
        {
          label: "全部用户查询",
          value: -1
        },
        {
          label: "公司用户",
          value: 1
        },
        {
          label: "家庭用户",
          value: 0
        },
        {
          label: "自提客户",
          value: 2
        },
        {
          label: "无地址用户",
          value: 3
        }
      ],
      areaOptions: [], // 省市区
      props: {
        value: "citie",
        label: "citie",
        children: "cities",
        emitPath: true
      },
      searchUserName: "",
      orderReturn: "",
      deliveryUserName: "",
      dateTimeRange: [],
      // 回桶单 sssssssssssssssssssssssssssssssss
      backTong: {
        list: []
      },
      backBucketDialog: false,
      backBucketForm: {
        showList: [],
        isShowReplace: false,
        brandList: [],
        allBrandList: [],
        allBrandIndex: 0,
        brandIndex: 0,
        bucketNum: "",
        currentOrderNum: "",
        payTType: ""
      },

      // 订单页面新客户下单跳转
      orderDriver: {
        flag: false,
        formalDriverDialog: false, // 固定送水员dialog
        formalDriverList: [], // 固定送水员列表
        thisFormalDriver: "", // 选中的固定送水员
        orderNum: "", // 当前的订单
        orderId: "", // 当前订单ID
        orderStatus: "",
        state: ""
      },
      // csl ----------------------------------
      pagesData: {
        pageTotal: 0,
        currentPage: 1, // 分页当前页
        currentPageSizes: [10, 15, 20], // 每页数据条数
        pageSize: 10 // 当前分页数据条数
      },

      userIDList: [],
      multipleSelection: [],
      selectedTotalList: [],
      // 退桶
      returnBucketDialog: false, // 退桶弹窗
      returnBucketData: {
        brandList: [],
        brandId: "",
        brandNum: "",
        replaceBrandId: "",
        replaceBrandNum: "",
        money: "",
        payId: "",
        orderNumber: "",
        userId: "",
        // add by yxw 2020--2-28
        newOrder: ""
      },
      returnBucketSelectOptions: {
        brandList: [],
        replaceBrandList: [],
        payType: [
          { name: "微信", payId: "0" },
          { name: "支付宝", payId: "1" },
          { name: "现金", payId: "2" }
        ]
      },

      // 时间筛选
      dateSelect: {
        disabledDate(time) {
          return (
            time.getTime() <
            new Date(
              JSON.parse(that.Cookies.get("storeInfo")).startTime
            ).getTime() -
            8.64e7 || time.getTime() > Date.now()
          )
        }
      },
      fuckLoading1: false,
      fuckLoading2: false,
      fuckLoading3: false,
      fuckLoading4: false,
      fuckLoading5: false,
      fuckLoading60: false,
      fuckLoading6: false,
      fuckLoading7: false,
      fuckLoading8: false,
      fuckLoading9: false,
      fuckLoading10: false,
      // 第N乱改需求之 修改月结
      monthlyKnots: {
        redisMoney: "0.00",
        downRedisMoney: "0.00",
        downMoney: "0.00",
        upRedisMoney: "0.00",
        totalMoney: "0.00",
        upMoney: "0.00"
      },
      bankMoneyTotal: {
        offline: "0.00",
        online: "0.00",
        received: "0.00",
        offlineReceived: "0.00",
        receivable: "0.00",
        onlineReceived: "0.00"
      },
      receivableVisible: false,
      receivableData: {},
      payMoneyNum: "",
      noUseWater: [],
      waterMoneyLii: {
        losePrice: "0.00",
        practical: "0.00",
        weiXinPrice: "0.00",
        bankPrice: "0.00",
        hdfkPrice: "0.00"
      },
      refundVisible: false,
      WaterPiao: {},
      rowListPiao: {},
      textShow: false,
      // new demand new message-----------------
      newDemandMsgVisible: false, // first
      newMsgInnder: false, // second
      newDemandForm: {
        newMsgType: 0, // 0tz 1 hd
        msgTemplate: "",
        msgTitle: "",
        msgContent: "",
        saveMsgTem: false,
        startTime: "",
        endTime: ""
      },
      msgDateDisable: {
        disabledDate(time) {
          var yesterdays = Date.now() - 24 * 60 * 60 * 1000
          return time.getTime() < yesterdays
        }
      },
      msgTempList: [],
      newMsgThreeInnder: false,
      showUserListType: 1, // 1 单个 2 all，3全部  已选客户名称显示
      choiceUserList: "",
      editMsgTemForm: {
        editMsgTitle: "",
        editMsgCont: "",
        templateId: "",
        templateType: ""
      },
      // new short msg begin============================
      newDemandMsgVisible2: false, // first
      newMsgInnder2: false, // second
      newDemandForm2: {
        newMsgType: 4, // 4tz 5hd
        msgTemplate: "",
        msgTitle: "",
        msgContent: "",
        saveMsgTem: false
      },
      msgDateDisable2: {
        disabledDate(time) {
          var yesterdays = Date.now() - 24 * 60 * 60 * 1000
          return time.getTime() < yesterdays
        }
      },
      msgTempList2: [],
      newMsgThreeInnder2: false,
      showUserListType2: 1, // 1 单个 2 all，3全部  已选客户名称显示
      choiceUserList2: "",
      editMsgTemForm2: {
        editMsgTitle: "",
        editMsgCont: "",
        templateId: "",
        templateType: ""
      },
      currentOrderData: "", // 要指派的订单
      buckFlag: 0,
      waterMark: 0,
      butcketlist: [],
      ticketList: [],
      imgUri: this.$imgUri,
      pageFlag: 0,

      tapMenuValue: 0,
      isKey: "",
      isKey2: "",

      tabBox: [
        {
          label: "0",
          value: "全部订单"
        },
        {
          label: "1",
          value: "待处理"
        },
        {
          label: "2",
          value: "已完成"
        }
      ],
      tabIndex: 0,
      list: [],
      page: 1,
      pageSize: 10,
      orderSum: 0,
      orderSumMoney: "0.00",
      selectOptions: [
        {
          value: {
            order: "",
            state: ""
          },
          label: "全部订单"
        },
        {
          value: {
            order: 2,
            state: 0
          },
          label: "待发单"
        },
        {
          value: {
            order: 1,
            state: 0
          },
          label: "待接单"
        },
        {
          value: {
            order: 3,
            state: 0
          },
          label: "已发货"
        },
        {
          value: {
            order: 3,
            state: 3
          },
          label: "已签收"
        },
        {
          value: {
            order: 10,
            state: 0
          },
          label: "已完成"
        },
        {
          value: {
            order: 8,
            state: 0,
            returnState: 0
          },
          label: "退货(客户申请退货中)"
        },
        {
          value: {
            order: 8,
            state: 0,
            returnState: 1
          },
          label: "退款(客户申请退款中)"
        },
        {
          value: {
            order: 8,
            state: 0,
            returnState: 2
          },
          label: "已退货"
        },
        {
          value: {
            order: 8,
            state: 0,
            returnState: 3
          },
          label: "已退款"
        }
      ],
      selectVal: "",
      orderUserTypeOptions: [
        {
          value: "0",
          label: "家庭"
        },
        {
          value: "1",
          label: "公司"
        }
      ],
      orderPayTypeOptions: [
        {
          value: "1",
          label: "微信支付"
        },
        {
          value: "5",
          label: "支付宝支付"
        },
        {
          value: "2",
          label: "线下付款"
        },
        {
          value: "3",
          label: "月付"
        },
        {
          value: "6",
          label: "水票支付"
        },
        {
          value: "7",
          label: "银行转账"
        }
      ],
      orderDeliveryOptions: [],
      orderSelect: {
        userType: "", // 0个人 1公司
        payType: "", // 1 微信  2 线下付款   3月付  4 钱包  5支付宝
        deliveryName: "",
        // date: [JSON.parse(that.Cookies.get("storeInfo")).startTime, that.$util.toDate(new Date()).noTime]
        date: []
      },
      badgeList: {
        today: 0,
        yesterday: 0,
        all: 0,
        daiPaiDan: 0,
        daiJieDan: 0,
        huitui: 0,
        yiFaHuo: 0,
        yiQianShou: 0,
        yiWanCheng: 0,
        returnNum: 0,
        bucketNum: 0,
        pledgeBuckApplyNum: 0,
        backOrders: 0
      },
      formalDriverDialog: false, // 固定送水员dialog
      formalDriverList: [], // 固定送水员列表
      thisFormalDriver: "", // 选中的固定送水员
      currentOrderNum: "", // 当前的订单
      // 订单详情/修改订单
      orderDetailDialog: false,
      orderDetailData: { details: "", todayBuckRecord: [] },
      orderDetailRow: "",
      areaOptions: [], // 省市区
      props: {
        value: "citie",
        label: "citie",
        children: "cities",
        emitPath: true
      },

      // 手填订单
      handOrderList: [],
      handOrderCount: 0,
      handPage: 1,
      handOrderDialog: false,
      handOrderForm: {
        order: "",
        name: "",
        phone: "",
        addressMap: "",
        address: "",
        isLift: false, // 是否是电梯房
        isCompanyAddress: false,
        companyName: "", // 公司名称
        floor: "", // 选择楼层
        flag: "1", // 0自定义  1选择商品
        customGoods: [
          // 自定义商品
          {
            name: "",
            num: "",
            price: "",
            total: 0
          }
        ],
        sendGoods: {
          list: [],
          changeList: [],
          checkList: [],
          searchKey: []
        },
        totalNum: 0, // 商品件数
        totalMoney: 0, // 订单总额
        payName: "",
        orderNum: "",
        orderTime: ""
      },
      mapFocus: false,
      handOrderSelectOptions: [
        {
          label: "一层",
          value: "1"
        },
        {
          label: "二层",
          value: "2"
        },
        {
          label: "三层",
          value: "3"
        },
        {
          label: "四层",
          value: "4"
        },
        {
          label: "五层",
          value: "5"
        },
        {
          label: "六层",
          value: "6"
        },
        {
          label: "七层及以上",
          value: "7"
        }
      ],
      handOrderRules: {
        name: [{ required: true, message: "请填写姓名", trigger: "blur" }],
        phone: [
          { required: true, message: "请填写电话号码", trigger: "blur" },
          { validator: checkPhone, trigger: "change" }
        ],
        addressMap: [
          { required: true, message: "请选取地址", trigger: "blur" }
        ],
        address: [
          { required: true, message: "请填写详细地址", trigger: "blur" }
        ],
        floor: [{ required: true, message: "请选择楼层", trigger: "blur" }]
      },
      mapDialog: false,
      sendGoodsDialog: false,

      // 时间筛选
      dateSelect: {
        disabledDate(time) {
          return (
            time.getTime() <
            new Date(
              JSON.parse(that.Cookies.get("storeInfo")).startTime
            ).getTime() -
            8.64e7 || time.getTime() > Date.now()
          )
        }
      },

      isLoading: false,
      // 操作日志
      operateData: {
        dialog: false,
        orderNumber: "",
        list: []
      },
      AssetsList: {
        lastOwnBuckDetails: [],
        totalPledgeBuck: 0,
        residueBuckDetails: [],
        totalPledgeBuckPrice: 0,
        thisBuck: 0,
        lastBuck: 0,
        residue: 0
      },
      // 图片预览相关
      imagePreviewVisible: false,
      previewImageList: [],
      currentImageIndex: 0,
      // 批量送达相关
      batchDeliveredVisible: false, // 批量送达对话框显示状态
      batchDeliveredForm: {
        selectedOrderCount: 0, // 选中的订单数量
        uploadedImages: [], // 已上传的图片列表
        uploading: false, // 上传状态
        processing: false // 处理状态
      },
      // 批量回退相关
      batchBackVisible: false, // 批量回退对话框显示状态
      batchBackForm: {
        selectedOrderCount: 0, // 选中的订单数量
        reason: '', // 回退原因
        processing: false // 处理状态
      },
      batchUploadData: {} // 上传时的额外数据
    }
  },
  components: {
    orderwuliu,
    orderexception,
    orderbackadmin,
    orderziti,
    orderwuliudetail,
    orderlocation,
  },
  computed: {
    countSendTotal() {
      let that = this
      let list = that.handOrderForm.sendGoods.checkList
      let totalPrice = 0
      list.forEach(function (item, index) {
        totalPrice = that.$util.add(
          that.$util.mul(item.num, item.newPrice),
          totalPrice
        )
      })
      return totalPrice
    },
    countCustomTotal() {
      let that = this
      let list = that.handOrderForm.customGoods
      let totalPrice = 0
      list.forEach(function (item, index) {
        totalPrice = that.$util.add(item.total, totalPrice)
      })
      return totalPrice
    },
    // 计算手填订单商品件数
    countHandOrderCheckList() {
      let that = this
      let list = that.handOrderForm.sendGoods.checkList
      let num = 0
      list.forEach(function (item) {
        num = that.$util.add(item.num, num)
      })
      return num
    },
    tableHeight() {
      // console.log(this.$store.getters.getGlobalHeight)
      let height = Number(this.$store.getters.getGlobalHeight) - 300
      if (height >= 300) {
        return height
      } else {
        return 300
      }
    },
    inTableHeight() {
      let height = this.$store.getters.getGlobalHeight
      if (height >= 400) {
        return parseInt(this.$util.mul(height, 0.5))
      } else {
        return 400
      }
    },
    // 编辑订单计算订单总价格
    countEditOrderMoney() {
      let that = this
      let arr1 = that.orderDetailData.specList || []
      let arr2 = that.orderDetailData.shopGroupList || []
      // item.price?item.price:item.doller
      // standardNumber
      let arr = arr1.concat(arr2)
      let money = 0
      arr.forEach((item) => {
        money = that.$util.add(
          that.$util.mul(item.price || item.doller, item.standardNumber),
          money
        )
      })
      return money
    }
  },
  created() {
    // 获取管理员店铺信息
    this.adminStoreInfo = JSON.parse(this.Cookies.get("adminStoreInfo") || '{}');
  },
  mounted() {
    let that = this

    that.storeId = that.Cookies.get("storeId")

    window.addEventListener(
      "message",
      function (event) {
        // 接收位置信息，用户选择确认位置点后选点组件会触发该事件，回传用户的位置信息
        // console.log("location")
        var loc = event.data
        if (loc && loc.module == "locationPicker") {
          // 防止其他应用也会向该页面post信息，需判断module是否为'locationPicker'
          // console.log("location", loc)
          that.handOrderForm.addressMap = loc.poiaddress
        }
      },
      false
    )

    if (that.$route.params.type == "home") {
      // that.tabIndex = that.$route.params.flag
      // that.clickTab(that.$route.params.flag)
      // console.log(that.$route.params.state, "that.$route.params.statethat.$route.params.statethat.$route.params.statethat.$route.params.statethat.$route.params.state")
      that.tapMenuValue = that.$route.params.state
    }
    if (that.$route.params.type == "msg") {
      //   that.goOrderDetail({ orderId: that.$route.params.orderId })
      if (that.$route.params.source == "退款") {
        that.tapMenu(8)
      }
    }

    that.deliveryLoad()
    that.load()
    that.getWaterMark()
  },
  watch: {},
  methods: {
    // 复制文本到剪贴板
    copyToClipboard(text) {
      if (!text) {
        this.$message.warning('内容为空，无法复制');
        return;
      }

      // 创建一个临时的textarea元素
      const textarea = document.createElement('textarea');
      textarea.value = text;
      textarea.style.position = 'fixed';
      textarea.style.opacity = '0';
      document.body.appendChild(textarea);

      try {
        // 选中文本并复制
        textarea.select();
        document.execCommand('copy');
        this.$message.success('复制成功');
      } catch (err) {
        console.error('复制失败:', err);
        this.$message.error('复制失败，请手动复制');
      } finally {
        // 清理临时元素
        document.body.removeChild(textarea);
      }
    },

    // 显示订单操作记录
    showOrderLog(orderNum) {
      if (!orderNum) {
        this.$message.error('订单号不能为空');
        return;
      }

      this.currentOrderNum = orderNum;
      this.showOrderLogModal = true;
      this.getOrderLog(orderNum);
    },

    // 获取订单操作记录
    getOrderLog(orderNum) {
      this.orderLogLoading = true;
      this.orderLogData = null;

      this.$post('/szmb/msg/selectmsgbyordernum', {
        orderNum: orderNum
      }).then((res) => {
        this.orderLogLoading = false;
        if (res.code == 1) {
          // 如果返回的是单个对象，转换为数组
          if (res.data && !Array.isArray(res.data)) {
            this.orderLogData = [res.data];
          } else {
            this.orderLogData = res.data || [];
          }
        } else {
          this.orderLogData = null;
          if (res.data !== '暂无数据') {
            this.$message.error(res.data || '获取操作记录失败');
          }
        }
      }).catch((error) => {
        this.orderLogLoading = false;
        this.orderLogData = null;
        this.$message.error('网络请求失败');
      });
    },

    // 关闭订单操作记录弹窗
    closeOrderLogModal() {
      this.showOrderLogModal = false;
      this.orderLogData = null;
      this.orderLogLoading = false;
      this.currentOrderNum = '';
    },

    refreshPhone(orderId) {
      let that = this
      that.$get("/jddj/refreshPhone", { orderId: orderId }).then((res) => {
        if (res.code == 1) {
          that.$message.success("刷新成功")
          that.load()
        }
      })
    },
    changeIndex(v, v1) {
      console.log(v)
      if (this.discountData.list[v].userPrice) {
        this.$message.error("请先移除商品优惠，在设置其他");
        return false;
      }
      this.$set(this.discountData.list[v], 'chooseListIndex', v1);
      this.discountData.list[v].userPrice = 0;
      this.discountData.list[v].send = 0;
      this.discountData.list[v].buy = 0;
    },
    getWaterMark() {

      this.$post("/smzcwcuse/selectMark", { storeId: this.Cookies.get("storeId") }).then((res) => {
        if (res.code == 1) {
          this.waterMark = res.data
        } else {
          this.waterMark = 0
        }
      })
    },
    // 新订单
    tapMenu(e) {
      let that = this
      that.tapMenuValue = e
      that.selectedOrders = [];
      that.checked = false;
      if (e == 10) {
        // 退桶订单
        that.page = 1
        that.lookUpPledBucket1();
        return false;
      }
      if (e == 11) {
        // 水票
        that.page = 1
        that.getticket();
        return false;
      }
      if (e == 8) {
        that.selectOptions = [
          {
            value: {
              order: 8,
              state: 0,
              returnState: 0
            },
            label: "退货(客户申请退货中)"
          },
          {
            value: {
              order: 8,
              state: 0,
              returnState: 1
            },
            label: "退款(客户申请退款中)"
          },
          {
            value: {
              order: 8,
              state: 0,
              returnState: 2
            },
            label: "已退货"
          },
          {
            value: {
              order: 8,
              state: 0,
              returnState: 3
            },
            label: "已退款"
          }
        ]
      }
      that.page = 1
      // that.clearSearch()
      that.load()
    },

    // 退桶记录表
    lookUpPledBucket1() {
      let that = this
      let isurl = "/szmb/newdebtbuckcontroller/selectbucketlist"
      let o = {
        storeId: that.Cookies.get("storeId"),
        brandId: -1,
        stateTime: that.orderSelect.date ? that.orderSelect.date[0] : "",
        endTime: that.orderSelect.date ? that.orderSelect.date[1] : "",
        ordersource: that.ordersource,
        index: that.page,
        pageSize: that.pageSize,
        name: that.isKey,
        address: that.isKey2,
        // userId: that.userDetail.userId,
        // source: that.pledBucketId,
        // payType: that.refundId
      }
      that.fuckLoading3 = true
      that.$post(isurl, o).then((res) => {
        that.fuckLoading3 = false
        if (res.code == 1) {
          that.butcketlist = res.data.list
          that.orderSum = res.data.total
        } else {
          that.butcketlist = []
          that.orderSum = 0
        }
      })
    },
    // 水票记录表
    getticket() {
      let that = this
      let data = undefined;
      let isurl = "/szmb/water/seluserwatercouponrecord"
      let o = {
        a: 1,
        storeId: that.Cookies.get("storeId"),
        waterId: data ? data.waterId : -1,
        waterName: data ? data.waterName : "",
        // userId: that.userDetail.userId,
        userName: that.isKey,
        address: that.isKey2,
        price: data ? data.price : "",
        pageNo: that.page,
        pageSize: that.pageSize,
        stateTime: that.date
          ? that.date[0]
          : "",
        endTime: that.date
          ? that.date[1]
          : "",
        payMent: that.remarkId
          ? that.remarkId
          : -1,
        type: that.makeSureId
          ? that.makeSureId
          : -1
      }
      that.fuckLoading3 = true
      that.$post(isurl, o).then((res) => {
        that.fuckLoading3 = false

        if (res.code == 1) {
          that.ticketList = res.data.list
          that.orderSum = res.data.count
        } else {
          that.ticketList = []
          that.orderSum = 0
        }
      })
    },
    // 查看客户历史押桶数据
    goUserBucketRecord(id) {
      let that = this
      that.$router.push({
        name: "usermanage",
        params: {
          _from: "orderAdmin",
          userId: id,
          orderNum: "",
          type: "固定"
        }
        // params: { type: "msg", userId: id }
      })
    },

    // 订单数据table样式
    cellStyle(e) {
      if (e.columnIndex == 0) {
        return "font-size:13px;"
      } else {
        return "text-align:center;font-size:13px;color:##333C48;"
      }
    },
    headerCellStyle(e) {
      if (e.columnIndex == 0) {
        return ""
      } else {
        return "text-align:center;"
      }
    },
    handleClose(done) {
      this.$confirm("确认关闭？")
        .then((_) => {
          done()
        })
        .catch((_) => { })
    },
    // 加载送水员姓名
    deliveryLoad() {
      let that = this
      let isurl = "/szmb/szmsendmembercontroller/selectdelectstate"
      let o = {
        storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.orderDeliveryOptions = res.data
        }
      })
    },
    // 订单搜索
    orderToSearch() {
      let that = this
      that.page = 1
      that.load()
    },
    refreshData() {
      let that = this
      that.page = 1
      that.load()

    },
    lookAllGoods(index, flag) {
      let that = this
      if (flag) {
        that.list[index].specFlag = 1
      } else {
        that.list[index].specFlag = that.list[index].specList.length
      }
    },
    clearSearch() {
      let that = this
      that.orderSelect = {
        userType: "", // 0个人 1公司
        payType: "", // 1 微信  2 线下付款   3月付  4 钱包  5支付宝
        deliveryName: "",
        // date: [JSON.parse(that.Cookies.get("storeInfo")).startTime, that.$util.toDate(new Date()).noTime]
        date: ""
      }
      that.selectVal = ""
      that.isKey = ""
      that.isKey2 = ""
      that.ordersource = ""
      that.load()
    },
    load(flag) {
      let that = this
      that.isLoading = true
      if (flag) {
        that.page = 1
      }
      let isurl = "/szmb/szmborder/orderlistpc"
      let orderStatus = -1
      let state = -1
      let returnState = ""
      if (that.selectVal) {
        let orderStatusOptions = JSON.parse(that.selectVal)
        orderStatus = orderStatusOptions.order
        state = orderStatusOptions.state
        returnState =
          orderStatusOptions.returnState === ""
            ? ""
            : orderStatusOptions.returnState
      }
      let time = ""
      if (that.tapMenuValue == 0) {
        time = that.$util.toDate(new Date()).noTime
      } else if (that.tapMenuValue == 1) {
        time = that.$util.toDate(new Date().getTime() - 1000 * 60 * 60 * 24)
          .noTime
      } else if (that.tapMenuValue == 3) {
        // 待发单
        orderStatus = 1
        state = 0
      } else if (that.tapMenuValue == 12) {
        // 回退订单
        orderStatus = 1
        state = 0
      } else if (that.tapMenuValue == 4) {
        // 已发单
        orderStatus = 3
        state = 1
        // } else if (that.tapMenuValue == 5) {
        //   // 已发货
        //   orderStatus = 3
        //   state = 0
      } else if (that.tapMenuValue == 6) {
        // 已签收
        orderStatus = 3
        state = 3
      } else if (that.tapMenuValue == 7) {
        // 已完成
        orderStatus = 10
        state = 0
      } else if (that.tapMenuValue == 8) {
        // 退货退款
        orderStatus = 8
      } else if (that.tapMenuValue == 9) {
        // 押桶申请中
        state = 4
      } else if (that.tapMenuValue == 10) {
        // 退桶订单
        that.page = 1
        that.lookUpPledBucket1();
      } else if (that.tapMenuValue == 11) {
        // 水票
        that.page = 1
        that.getticket();
      }

      let o = {
        storeId: that.storeId,
        userType: that.orderSelect.userType,
        deliveryName: that.orderSelect.deliveryName,
        payMent: that.orderSelect.payType,
        ordersource: that.ordersource,
        time: time, // 今日和昨日
        pageNo: that.page,
        pageSize: that.pageSize,
        name: that.isKey,
        address: that.isKey2,
        orderStatus: orderStatus, // 订单状态
        state: state, // 订单状态
        returnState: returnState, // 订单状态
        startTime: that.orderSelect.date ? that.orderSelect.date[0] : "",
        endTime: that.orderSelect.date ? that.orderSelect.date[1] : ""
      }

      // 如果是回退订单tab，添加back=1参数
      if (that.tapMenuValue == 12) {
        o.back = 1
      }
      that.$post(isurl, o).then((res) => {
        that.isLoading = false
        if (res.code == 1) {
          // 为每个item添加isChecked属性
          that.list = res.data.userList.map(item => {
            item.isChecked = false;
            return item;
          });
          that.orderSum = res.data.count
        } else {
          that.list = []
          that.orderSum = 0
          that.$message.error(res.data)
        }
      })
      that.$post("/szmb/szmborder/orderlistpcanalysis", o).then((res) => {
        if (res.code == 1) {
          that.orderSumMoney = res.data.totalMoney
          that.badgeList = {
            today: res.data.today,
            yesterday: res.data.yesterday,
            all: res.data.all,
            daiPaiDan: res.data.daiPaiDan,
            daiJieDan: res.data.daiJieDan,
            huitui: res.data.huitui,
            yiFaHuo: res.data.yiFaHuo,
            yiQianShou: res.data.yiQianShou,
            yiWanCheng: res.data.yiWanCheng,
            returnNum: res.data.returnNum,
            bucketNum: res.data.bucketNum,
            pledgeBuckApplyNum: res.data.pledgeBuckApplyNum,
            backOrders: res.data.backOrders || 0
          }
        } else {
          that.orderSumMoney = "0.00"
          that.$message.error(res.data)
        }
      })
    },
    // 退货退款同意拒绝
    dealSale(id, flag) {
      let that = this
      let isurl = "/szmb/orderreturn/update"
      let o = {
        retreatId: id,
        state: flag
      }

      that
        .$confirm("确定执行此操作?", "温馨提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          that.$post(isurl, o).then((res) => {
            if (res.code == 1) {
              that.$message({
                message: "处理成功!",
                type: "success"
              })
              that.orderDetailDialog = false
              that.load()
            } else {
              that.$message.error(res.data)
            }
          })
        })
        .catch(() => {
          that.$message({
            type: "info",
            message: "已取消该操作"
          })
        })
    },

    // 分页
    clickPage(e) {
      let that = this
      that.page = e
      that.load()
      // if (that.tabIndex == 0) {
      //   that.load1()
      // } else if (that.tabIndex == 1) {
      //   that.load2()
      // } else {
      //   that.load3()
      // }
    },
    clickPage2(e) {
      let that = this
      that.page = e
      that.lookUpPledBucket1()
    },
    clickPage3(e) {
      let that = this
      that.page = e
      that.getticket();
    },
    // 分页
    clickHandPage(e) {
      let that = this
      that.handPage = e
      that.openHandOrder()
    },
    // 切换tab
    clickTab(e) {
      // console.log(e)
      let that = this
      // if (e == 0) {
      //   that.page = 1
      //   that.orderSelect = {
      //     userType: "", // 0个人 1公司
      //     payType: "", // 1 微信  2 线下付款   3月付  4 钱包  5支付宝
      //     deliveryName: "",
      //     date: [JSON.parse(that.Cookies.get("storeInfo")).startTime, that.$util.toDate(new Date()).noTime]
      //   }
      //   that.list = []
      //   that.load1()
      // }
      // if (e == 1) {
      //   that.page = 1
      //   that.list = []
      //   that.selectVal = ""
      //   that.load2()
      // }
      // if (e == 2) {
      //   that.page = 1
      //   that.list = []
      //   that.load3()
      // }
      that.page = 1
      that.orderSelect = {
        userType: "", // 0个人 1公司
        payType: "", // 1 微信  2 线下付款   3月付  4 钱包  5支付宝
        deliveryName: "",
        date: [
          JSON.parse(that.Cookies.get("storeInfo")).startTime,
          that.$util.toDate(new Date()).noTime
        ]
      }
      that.selectVal = ""
      that.list = []
      that.load()
    },

    // 查看订单详情
    goOrderDetail(row) {
      let that = this
      let isurl = "/szmb/szmborder/selectorderone"
      let o = {
        orderId: row.orderId
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.getThreeCitys()
          that.orderDetailData = res.data
          that.orderDetailData.region = [
            res.data.provinces,
            res.data.city,
            res.data.arae
          ]
          that.orderDetailRow = { orderId: row.orderId }
          that.updateUserState(true)
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 获取地区信息
    getThreeCitys() {
      var that = this
      that
        .$post("/dpt/address/shanghai")
        .then((req) => {
          if (req.code === 1) {
            that.areaOptions = req.data
          } else {
            this.$message.error(req.data)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },

    // 查看手填订单
    openHandOrder() {
      let that = this
      let isurl = "/szmb/szmborderstorecontroller/selectorderbystoreid"
      let o = {
        storeId: that.Cookies.get("storeId"),
        pageNo: that.handPage,
        pageSize: 10
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.handOrderList = res.data.list
          that.handOrderCount = res.data.count
        } else {
          that.handOrderList = []
        }
      })
      that.pageFlag = 1
    },
    // 新增手填订单
    addNewHandOrder() {
      let that = this
      that.handOrderForm = {
        order: "",
        name: "",
        phone: "",
        addressMap: "",
        address: "",
        isLift: false, // 是否是电梯房
        isCompanyAddress: false,
        companyName: "",
        floor: "", // 选择楼层
        flag: "1", // 0自定义  1选择商品
        customGoods: [
          // 自定义商品
          {
            name: "",
            num: "",
            price: "",
            total: 0
          }
        ],
        sendGoods: {
          list: [],
          changeList: [],
          checkList: []
        },
        totalNum: 0, // 商品件数
        totalMoney: 0, // 订单总额
        payName: "",
        orderNum: "",
        orderTime: ""
      }
      that.handOrderDialog = true
    },
    // 编辑手填订单
    editHandOrder(orderNum) {
      let that = this
      let isurl = "/szmb/szmborderstorecontroller/selectbyordernum"
      let o = {
        orderNum: orderNum
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          let newList = []
          // let arr = []
          if (res.data.orderSource) {
            // 选择商品
            let list = res.data.orderStoreProductVos
            newList = list.map(function (item) {
              let json = {}
              if (item.model) {
                let arr = item.model.split(",")
                json = {
                  brandName: item.name,
                  content: item.url,
                  num: item.num,
                  newPrice: item.price,
                  skuId: item.id,
                  skuName: arr[0],
                  spuName: arr[1]
                }
              } else {
                json = {
                  brandName: item.name,
                  content: item.url,
                  num: item.num,
                  newPrice: item.price,
                  skuId: item.id,
                  skuName: "",
                  spuName: ""
                }
              }

              return json
            })
          } else {
            // 自定义
            let list = res.data.orderStoreProductVos
            newList = list.map(function (item) {
              let json = {
                name: item.name,
                num: item.num,
                price: item.price,
                total: item.allPrice
              }
              // console.log(json, "json")
              return json
            })
          }
          let customJson = [
            // 自定义商品
            {
              name: "",
              num: "",
              price: "",
              total: 0
            }
          ]

          that.handOrderForm.name = res.data.userName
          that.handOrderForm.phone = res.data.userPhone
          that.handOrderForm.addressMap = res.data.userArea
          that.handOrderForm.address = res.data.userAddress
          that.handOrderForm.floor = res.data.level
          that.handOrderForm.isLift = !!res.data.isElevator
          that.handOrderForm.isCompanyAddress = !!res.data.isEnterprise
          that.handOrderForm.companyName = res.data.r3
          that.handOrderForm.flag = res.data.orderSource ? "1" : "0"
          that.handOrderForm.sendGoods.checkList = res.data.orderSource
            ? newList
            : []
          that.handOrderForm.sendGoods.changeList = res.data.orderSource
            ? newList
            : []
          that.handOrderForm.customGoods = res.data.orderSource
            ? customJson
            : newList
          that.handOrderForm.payName = res.data.payName
          that.handOrderForm.orderNum = res.data.orderNum
          that.handOrderForm.orderTime = res.data.orderTime
          that.handOrderDialog = true
        } else {
          that.$message.error(res.data)
        }
      })
    },
    getLocation() {
      let that = this
      that.mapDialog = true
    },
    mapSure() {
      let that = this
      that.mapDialog = false
      this.$refs.mapInput.$el.querySelector("input").focus()
    },
    // 新增自定义商品
    addCustomGoods() {
      let that = this
      that.handOrderForm.customGoods.push({
        name: "",
        num: "",
        price: "",
        total: 0
      })
    },
    // 删除自定义商品
    delCustomGoods() {
      let that = this
      let length = that.handOrderForm.customGoods.length
      if (length != 0) {
        that.handOrderForm.customGoods.pop()
      }
    },
    countSingleTotal(index) {
      let that = this
      let row = that.handOrderForm.customGoods[index]
      let num = row.num
      let price = row.price
      if (isNaN(num) || isNaN(price)) {
        return
      }
      row.total = that.$util.mul(num, price)
    },
    // 选择商品
    chooseGoods() {
      let that = this
      let isurl = "/szmb/szmshopgroupcontroller/selectskulistall"
      let o = {
        storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.handOrderForm.sendGoods.list = res.data
          that.sendGoodsDialog = true
          that.$nextTick(function () {
            let list = that.handOrderForm.sendGoods.checkList

            if (list.length) {
              res.data.forEach((item) => {
                list.forEach((it) => {
                  if (it.id) {
                    if (item.skuId == it.id) {
                      item.num = it.num
                      this.$refs.sendGoodsElement.toggleRowSelection(item)
                    }
                  } else {
                    if (item.skuId == it.skuId) {
                      item.num = it.num
                      this.$refs.sendGoodsElement.toggleRowSelection(item)
                    }
                  }
                })
              })
            }
          })
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 商品选中
    sendGoodsCheck(val, row) {
      let that = this
      let flag = val.indexOf(row) != -1 // 判断选中还是取消 flag=true 选中  false 是取消
      if (flag) {
        that.handOrderForm.sendGoods.changeList.push(row)
      } else {
        that.handOrderForm.sendGoods.changeList = that.$util.arrRemoveJson(
          that.handOrderForm.sendGoods.changeList,
          "skuId",
          row.skuId
        )
      }
      // console.log(that.handOrderForm.sendGoods.changeList, "changeList")

      // that.handOrderForm.sendGoods.changeList = e
    },
    // 确定选中的商品
    sureSendGoods() {
      let that = this

      let list = that.handOrderForm.sendGoods.list
      let changeList = that.handOrderForm.sendGoods.changeList
      let checkList = []
      list.forEach(function (item) {
        changeList.forEach(function (it) {
          if (item.skuId == it.skuId) {
            checkList.push(item)
          }
        })
      })

      // that.handOrderForm.sendGoods.checkList = that.handOrderForm.sendGoods.changeList
      that.handOrderForm.sendGoods.checkList = checkList
      that.sendGoodsDialog = false
    },
    // 搜索商品
    toSearchProduct() {
      let that = this
      let isurl = "/szmb/szmshopgroupcontroller/selectshopgroupname"
      let o = {
        storeId: that.Cookies.get("storeId"),
        shopGroupName: that.handOrderForm.sendGoods.searchKey
      }

      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          let list = res.data

          if (list.length) {
            that.handOrderForm.sendGoods.list = res.data
            that.$nextTick(function () {
              let arr = that.handOrderForm.sendGoods.changeList
              list.forEach(function (item) {
                arr.forEach(function (it) {
                  if (item.skuId == it.skuId) {
                    that.$refs.sendGoodsElement.toggleRowSelection(item, true)
                    item.num = it.num
                  }
                })
              })
            })
          } else {
            that.$message.error("未搜索到相关商品")
          }
          // that.handOrderForm.sendGoods = res.data
        } else {
          that.$message.error("暂无数据")
        }
      })
    },
    // 手填订单提交
    handOrderSubmit() {
      let that = this
      let data = that.handOrderForm
      that.$refs.handOrderElement.validate((valid) => {
        if (valid) {
          if (data.isCompanyAddress) {
            if (!data.companyName) {
              that.$message.error("请填写公司名称")
              return false
            }
          }
          let orderCount = ""
          let orderMoney = ""
          let orderProducts = []
          if (data.flag == 1) {
            if (data.sendGoods.checkList.length == 0) {
              that.$message.error("请选择商品")
              return false
            }
          }

          if (data.flag == 1) {
            let list = data.sendGoods.checkList
            let newList = []
            list.forEach(function (item) {
              let json = {
                name: item.brandName,
                num: item.num,
                price: item.newPrice,
                allPrice: "",
                id: item.skuId,
                model: item.spuName + "," + item.skuName,
                url: item.content
              }
              orderMoney = that.$util.add(
                that.$util.mul(item.newPrice, item.num),
                orderMoney
              )
              newList.push(json)
            })
            orderCount = that.countHandOrderCheckList
            orderProducts = JSON.stringify(newList)
          } else {
            let list = data.customGoods
            let newList = []
            list.forEach((item) => {
              let json = {
                name: item.name,
                num: item.num,
                price: item.price,
                allPrice: item.total,
                id: item.id ? item.id : "",
                model: item.model ? item.model : "",
                url: item.url ? item.url : ""
              }
              newList.push(json)
              orderMoney = that.$util.add(item.total, orderMoney)
            })
            orderCount = that.countHandOrderCheckList
            orderProducts = JSON.stringify(newList)
          }
          let o = {
            userName: data.name,
            userPhone: data.phone,
            userArea: data.addressMap,
            userAddress: data.address,
            level: data.floor,
            isElevator: data.isLift ? 1 : 0,
            isEnterprise: data.isCompanyAddress ? 1 : 0,
            r3: data.companyName,
            orderProducts: orderProducts,
            storeId: that.Cookies.get("storeId"),
            orderCount: orderCount,
            orderMoney: orderMoney,
            orderSource: data.flag,
            header: "json"
          }
          let isurl = ""
          if (that.handOrderForm.orderNum) {
            isurl = "/szmb/szmborderstorecontroller/updateorder"
            o.orderNum = that.handOrderForm.orderNum
          } else {
            isurl = "/szmb/szmborderstorecontroller/addorder"
          }
          that.$post(isurl, o).then((res) => {
            if (res.code == 1) {
              that.$message({
                message: "保存成功!",
                type: "success"
              })
              that.$refs.handOrderElement.resetFields()
              that.openHandOrder()
              that.handOrderDialog = false
            } else {
              that.$message.error(res.data)
            }
          })
        } else {
          return false
        }
      })
    },
    // 选择固定送水员
    chooseFormalDriver(row) {
      let that = this
      that.currentOrderNum = row.orderNum
      that.isChangeDelivery = 0
      that.openFormalDriverList()
    },
    batchDispatch() {
      let that = this
      if (that.selectedOrders.length <= 0) {
        that.$message.error("请选择要派单的订单")
        return
      }
      that.currentOrderNum = that.selectedOrders.join(",")
      that.isChangeDelivery = 0
      that.openFormalDriverList()
    },
    changeDelivery(row) {
      let that = this
      that.currentOrderNum = row.orderNum
      that.isChangeDelivery = 1
      that.openFormalDriverList()
    },
    batchDispatchChange() {
      let that = this
      if (that.selectedOrders.length <= 0) {
        that.$message.error("请选择要派单的订单")
        return
      }
      that.currentOrderNum = that.selectedOrders.join(",")
      that.isChangeDelivery = 1
      that.openFormalDriverList()
    },
    // 批量回退
    batchBack() {
      let that = this
      if (that.selectedOrders.length <= 0) {
        that.$message.error("请选择要回退的订单")
        return
      }

      // 打开批量回退对话框
      that.batchBackVisible = true
      that.batchBackForm.selectedOrderCount = that.selectedOrders.length
      that.batchBackForm.reason = ''
    },
    // 批量送达
    batchDelivered() {
      let that = this
      if (that.selectedOrders.length <= 0) {
        that.$message.error("请选择要送达的订单")
        return
      }

      // 打开批量送达对话框（包含照片上传）
      that.batchDeliveredVisible = true
      that.batchDeliveredForm.selectedOrderCount = that.selectedOrders.length
      that.batchDeliveredForm.uploadedImages = []
      that.batchDeliveredForm.uploading = false
    },
    // 批量上传凭证前的验证
    beforeBatchVoucherUpload(file) {
      const isValidType = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'].includes(file.type)
      const isLt5M = file.size / 1024 / 1024 < 5

      if (!isValidType) {
        this.$message.error('只能上传 JPG、PNG、PDF 格式的文件!')
        return false
      }
      if (!isLt5M) {
        this.$message.error('文件大小不能超过 5MB!')
        return false
      }

      this.batchDeliveredForm.uploading = true
      return true
    },

    // 批量上传凭证成功回调
    handleBatchVoucherSuccess(response, file) {
      this.batchDeliveredForm.uploading = false
      if (response.code === 1) {
        // 添加到已上传列表
        this.batchDeliveredForm.uploadedImages.push({
          url: response.data || file.response,
          name: file.name,
          orderId: null // 稍后分配给具体订单
        })
        this.$message.success(`凭证上传成功! 已上传 ${this.batchDeliveredForm.uploadedImages.length} 张`)
      } else {
        this.$message.error('凭证上传失败: ' + (response.msg || response.data || '未知错误'))
      }
    },

    // 批量上传凭证失败回调
    handleBatchVoucherError(err) {
      this.batchDeliveredForm.uploading = false
      console.error('上传失败:', err)
      this.$message.error('凭证上传失败!')
    },

    // 删除已上传的图片
    removeBatchImage(index) {
      this.batchDeliveredForm.uploadedImages.splice(index, 1)
      this.$message.success('已删除凭证')
    },

    // 预览批量上传的图片
    previewBatchImages(imageList, index = 0) {
      this.previewImageList = [...imageList]
      this.currentImageIndex = index
      this.imagePreviewVisible = true
    },

    // 确认批量送达
    confirmBatchDelivered() {
      let that = this

      if (that.batchDeliveredForm.uploadedImages.length === 0) {
        that.$message.error("请先上传送达凭证")
        return
      }

      // 确认对话框
      this.$confirm(
        `确认批量送达选中的 ${that.selectedOrders.length} 个订单吗？\n已上传 ${that.batchDeliveredForm.uploadedImages.length} 张凭证`,
        "批量送达确认",
        {
          confirmButtonText: "确定送达",
          cancelButtonText: "取消",
          type: "warning",
          center: true
        }
      ).then(() => {
        // 执行批量送达
        that.executeBatchDeliveredWithVoucher()
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 执行带凭证的批量送达
    executeBatchDeliveredWithVoucher() {
      let that = this
      let selectedOrderNums = that.selectedOrders.slice() // 复制数组，避免修改原数组

      // 从订单列表中找到对应的orderId
      let orderIds = []
      selectedOrderNums.forEach(orderNum => {
        let order = that.list.find(item => item.orderNum === orderNum)
        if (order && order.orderId) {
          orderIds.push(order.orderId)
        }
      })

      if (orderIds.length === 0) {
        that.$message.error("未找到有效的订单ID，请刷新页面后重试")
        return
      }

      that.batchDeliveredForm.processing = true
      let successCount = 0
      let failCount = 0
      let totalCount = orderIds.length
      let uploadedImages = [...that.batchDeliveredForm.uploadedImages] // 复制图片列表

      // 显示进度提示
      that.$message({
        message: `开始批量送达 ${totalCount} 个订单...`,
        type: "info"
      })

      // 递归处理每个订单
      const processOrder = (index) => {
        if (index >= orderIds.length) {
          // 所有订单处理完成
          that.batchDeliveredForm.processing = false
          that.batchDeliveredVisible = false

          if (failCount === 0) {
            that.$message({
              message: `批量送达完成！成功处理 ${successCount} 个订单`,
              type: "success"
            })
          } else {
            that.$message({
              message: `批量送达完成！成功 ${successCount} 个，失败 ${failCount} 个`,
              type: "warning"
            })
          }
          // 清空选中的订单
          that.selectedOrders = []
          // 刷新列表
          that.load()
          return
        }

        // 处理当前订单
        let orderId = orderIds[index]

        // 第一步：上传凭证（如果有图片的话）
        const uploadVoucherForOrder = () => {
          return new Promise((resolve) => {
            // 为当前订单分配一张凭证图片（如果有的话）
            if (uploadedImages.length > 0) {
              let imageToUse = uploadedImages.shift() // 取出一张图片

              // 调用 updatePirurlByOrderId 接口上传凭证
              that.$post('/szmcordermaincontroller/updatePirurlByOrderId', {
                orderMainId: orderId,
                picurl: imageToUse.url
              }).then((res) => {
                if (res.code === 1) {
                  console.log(`订单 ${orderId} 凭证上传成功`)
                } else {
                  console.error(`订单 ${orderId} 凭证上传失败:`, res.data)
                }
                resolve() // 无论成功失败都继续下一步
              }).catch((error) => {
                console.error(`订单 ${orderId} 凭证上传请求失败:`, error)
                resolve() // 无论成功失败都继续下一步
              })
            } else {
              // 没有更多图片，直接继续
              resolve()
            }
          })
        }

        // 第二步：执行送达
        const executeDelivery = () => {
          let isurl = "/szmb/deliveryinfo/updatdelivery"
          let params = {
            orderId: orderId
          }

          that.$post(isurl, params).then((res) => {
            if (res.code == 1) {
              successCount++
            } else {
              failCount++
              console.error(`订单 ${orderId} 送达失败:`, res.data)
            }
            // 处理下一个订单
            processOrder(index + 1)
          }).catch((error) => {
            failCount++
            console.error(`订单 ${orderId} 送达请求失败:`, error)
            // 处理下一个订单
            processOrder(index + 1)
          })
        }

        // 按顺序执行：先上传凭证，再执行送达
        uploadVoucherForOrder().then(() => {
          executeDelivery()
        })
      }

      // 开始处理第一个订单
      processOrder(0)
    },
    // 确认批量回退
    confirmBatchBack() {
      let that = this

      if (!that.batchBackForm.reason.trim()) {
        that.$message.error("请输入回退原因")
        return
      }

      // 确认对话框
      this.$confirm(
        `确认批量回退选中的 ${that.selectedOrders.length} 个订单吗？\n回退原因：${that.batchBackForm.reason}`,
        "批量回退确认",
        {
          confirmButtonText: "确定回退",
          cancelButtonText: "取消",
          type: "warning",
        }
      ).then(() => {
        that.executeBatchBack()
      }).catch(() => {
        // 用户取消操作
      })
    },

    // 执行批量回退
    executeBatchBack() {
      let that = this
      let selectedOrderNums = that.selectedOrders.slice() // 复制数组，避免修改原数组

      // 从订单列表中找到对应的orderId
      let orderIds = []
      selectedOrderNums.forEach(orderNum => {
        let order = that.list.find(item => item.orderNum === orderNum)
        if (order && order.orderId) {
          orderIds.push(order.orderId)
        }
      })

      if (orderIds.length === 0) {
        that.$message.error("未找到有效的订单ID，请刷新页面后重试")
        return
      }

      that.batchBackForm.processing = true
      let successCount = 0
      let failCount = 0
      let totalCount = orderIds.length

      // 显示进度提示
      that.$message({
        message: `开始批量回退 ${totalCount} 个订单...`,
        type: "info"
      })

      // 递归处理每个订单
      const processOrder = (index) => {
        if (index >= orderIds.length) {
          // 所有订单处理完成
          that.batchBackForm.processing = false
          that.batchBackVisible = false

          if (failCount === 0) {
            that.$message({
              message: `批量回退完成！成功处理 ${successCount} 个订单`,
              type: "success"
            })
          } else {
            that.$message({
              message: `批量回退完成！成功 ${successCount} 个，失败 ${failCount} 个`,
              type: "warning"
            })
          }
          // 清空选中的订单
          that.selectedOrders = []
          // 刷新列表
          that.load()
          return
        }

        // 处理当前订单
        let orderId = orderIds[index]

        // 调用回退接口
        that.$post('/szmb/szmsendmembercontroller/backAdmin', {
          orderId: orderId,
          reason: that.batchBackForm.reason
        }).then((res) => {
          if (res.code === 1) {
            successCount++
          } else {
            failCount++
            console.error(`订单 ${orderId} 回退失败:`, res.data)
          }
          // 处理下一个订单
          processOrder(index + 1)
        }).catch((error) => {
          failCount++
          console.error(`订单 ${orderId} 回退请求失败:`, error)
          // 处理下一个订单
          processOrder(index + 1)
        })
      }

      // 开始处理第一个订单
      processOrder(0)
    },
    wuliu(row) {
      this.orderwuliuVisible = true
      this.$nextTick(() => {
        this.$refs.orderwuliu.init(row.orderId)
      })
    },
    exception(row) {
      this.orderexceptionVisible = true
      this.$nextTick(() => {
        this.$refs.orderexception.init(row.orderId)
      })
    },
    orderbackadmin(row) {
      this.orderbackadminVisible = true
      this.$nextTick(() => {
        this.$refs.orderbackadmin.init(row.orderId)
      })
    },
    ziti(row) {
      this.orderzitiVisible = true
      this.$nextTick(() => {
        this.$refs.orderziti.init(row.orderId)
      })
    },
    orderwuliudetail(row) {
      this.orderwuliudetailVisible = true
      this.$nextTick(() => {
        this.$refs.orderwuliudetail.init(row.orderId)
      })
    },
    openFormalDriverList() {
      let that = this
      that.formalDriverList = [] // 固定送水员列表
      that.thisFormalDriver = "" // 选中的固定送水员
      that.deliveryType = "driver" // 重置为送水员派送
      let isurl = "/szmb/szmsendmembercontroller/selectdelectstate"
      let o = {
        storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.formalDriverList = res.data
          that.formalDriverDialog = true
        } else {
          that.$message.error("请先添加送水员")
        }
      })
    },
    // 选择派送方式
    selectDeliveryType(type) {
      this.deliveryType = type
      if (type === 'self') {
        // 选择水站自送时，清除选中的送水员
        this.thisFormalDriver = ""
      }
    },
    // 处理水站自送
    handleMerchantDeliverSelf() {
      let that = this

      // 确认对话框
      this.$confirm('确认将订单设置为水站自送吗？', '水站自送确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 执行水站自送
        that.executeMerchantDeliverSelf()
      }).catch(() => {
        // 用户取消操作
      })
    },
    // 执行水站自送
    executeMerchantDeliverSelf() {
      let that = this
      let isurl = "/szmb/self/add"
      let o = {
        orderNumber: that.currentOrderNum,
        latAndLon: that.Cookies.get("storeLocal") || ""
      }

      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "水站自送设置成功!",
            type: "success"
          })
          that.load()
          that.selectedOrders = []
          that.formalDriverDialog = false
        } else {
          that.$message.error(res.data || "水站自送设置失败")
        }
      }).catch((error) => {
        console.error("水站自送设置失败:", error)
        that.$message.error("网络请求失败，请重试")
      })
    },
    // 选择固定送水员
    openFormalDriverListDebt(row) {
      let that = this
      that.buckFlag = 1;
      that.formalDriverList = [] // 固定送水员列表
      that.thisFormalDriver = "" // 选中的固定送水员
      that.currentOrderData = row
      // if (ostate == 1) {
      //   isurl = "/szmb/szmsendmembercontroller/selectdelectstate"
      // } else {
      let isurl = "szmb/szmsendmembercontroller/selectdelectstate2"
      // }

      let o = {
        storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then((res) => {
        console.log(res)
        if (res.code == 1) {
          that.formalDriverList = res.data
          that.formalDriverDialog = true
        }
      })
    },
    // 操作
    makeSureReturnBucketRecord(row, flag) {
      let that = this
      let isurl = "/szmb/newdebtbuckcontroller/updatebuckmoneystate"
      let o = {
        orderNumber: row.orderNumber,
        deliveryId: 0,
        state: flag,
        time: row.time
      }
      that.$post(isurl, o).then((res) => {
        console.log(res)
        if (res.code == 1) {
          that.$message({
            message: "操作成功!",
            type: "success"
          })
          that.lookUpPledBucket1()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    orderrefund(id) {
      // 先提示确认要不要退款
      this.$confirm('确定要退款吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.confirmrefund123(id)
      })
    },
    confirmrefund123(id) {
      let that = this
      that.$post("/szmb/orderreturn/adminreturn", {
        orderMainId: id
      }).then((res) => {
        if (res.code == 1) {
          that.$message.success('退款成功')
          that.load()
        }
      })
    },
    makeSureReturnBucketRecord1(row, flag) {

      let that = this
      this.$confirm("确认直接退款")
        .then((_) => {
          let isurl = "/szmb/newdebtbuckcontroller/updatebuckmoneystate"
          let o = {
            orderNumber: row.orderNumber,
            deliveryId: 0,
            state: flag,
            time: row.time
          }
          that.$post(isurl, o).then((res) => {
            console.log(res)
            if (res.code == 1) {
              that.$message({
                message: "操作成功!",
                type: "success"
              })
              that.lookUpPledBucket1()
            } else {
              that.$message.error(res.data)
            }
          })
        })
        .catch((_) => { })
    },
    // 选中固定送水员
    formalDriverSubmit() {
      let that = this

      // 如果选择水站自送
      if (that.deliveryType === 'self') {
        that.handleMerchantDeliverSelf()
        return
      }

      // 原有的送水员派送逻辑
      if (!that.thisFormalDriver) {
        that.$message.error("请选择固定送水员")
        return
      }
      if (that.buckFlag == 1) {

        let isurl = "/szmb/newdebtbuckcontroller/updatebuckmoneystate"
        let o = {
          orderNumber: that.currentOrderData.orderNumber,
          deliveryId: that.thisFormalDriver.id,
          time: that.currentOrderData.time
        }
        that.$post(isurl, o).then((res) => {
          console.log(res)
          if (res.code == 1) {
            that.$message({
              message: "指派成功",
              type: "success"
            })
            that.lookUpPledBucket1()

            that.formalDriverDialog = false
          } else {
            that.$message.error(res.data)
          }
        })
      } else {
        let data = that.thisFormalDriver
        let isurl = that.isChangeDelivery == 0 ? "/szmb/szmborder/selectdeliveryid" : "/szmb/szmborder/changeDeliveryId"
        let o = {
          orderId: that.currentOrderNum,
          deliveryId: data.id
        }
        that.$post(isurl, o).then((res) => {
          if (res.code == 1) {
            that.$message({
              message: "指派成功!",
              type: "success"
            })
            that.load()
            // that.updateUserState()
            that.selectedOrders = []
            that.formalDriverDialog = false
          } else {
            that.$message.error(res.data)
          }
        })
      }
    },
    // 代接单
    replaceOrder(e) {
      let that = this
      let isurl = "/szmb/storesendpaycontroller/insertsenddelivery"
      let order = e.order
      let id = e.id
      let o = {
        orderId: order,
        userId: id,
        latAndLon: that.Cookies.get("storeLocal")
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "接单成功!",
            type: "success"
          })
          // if (that.tabIndex == "0") {
          //   that.load1()
          // } else if (that.tabIndex == "1") {
          //   that.selectVal = ""
          //   that.load2()
          // } else {
          //   that.load3()
          // }
          that.load()
          // that.updateUserState()
          that.formalDriverDialog = false
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 撤销接单
    cancelOrder(e) {
      let that = this
      let order = e.orderId
      // let id = e.id
      let isurl = "/szmb/szmsendmembercontroller/back"
      let o = {
        orderId: order,
        // deliveryId: id
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "撤销成功!",
            type: "success"
          })
          that.load()
          // that.updateUserState()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    orderConfirm(e) {
      let that = this

      this.$confirm("确认代骑手点确认送达?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
        center: true
      })
        .then(() => {
          let order = e.orderId
          // let id = e.id
          let isurl = "/szmb/deliveryinfo/updatdelivery"
          let o = {
            orderId: order,
            // deliveryId: id
          }
          that.$post(isurl, o).then((res) => {
            if (res.code == 1) {
              that.$message({
                message: "确认送达成功!",
                type: "success"
              })
              that.load()
              // that.updateUserState()
            } else {
              that.$message.error(res.data)
            }
          })
        })
        .catch(() => {
        })
    },
    // 更新订单状态
    updateOrderState(e) {
      let that = this
      let isurl = "/szmb/szmborder/updatestate"
      let o = {
        orderId: e.orderId,
        orderStatus: e.orderStatus,
        state: e.state
      }
      if (e.type == "接单") {
        that.$post(isurl, o).then((res) => {
          if (res.code == 1) {
            that.message({
              message: "接单成功!",
              type: "success"
            })
            that.load()
          } else {
            that.$message.error(res.data)
          }
        })
      }
      if (e.type == "临时") {
        if (e.isAlert == "1") {
          that
            .$confirm(
              "该用户是您店铺的新客户，您可以在客户管理对该客户进行相关内容设置，是否设置？",
              "温馨提示",
              {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
              }
            )
            .then(() => {
              that.$router.push({
                name: "usermanage",
                params: {
                  _from: "orderAdmin",
                  userId: e.userId,
                  orderId: e.orderId,
                  orderStatus: e.orderStatus,
                  state: e.state,
                  type: "临时"
                }
              })
            })
            .catch(() => {
              that.$post(isurl, o).then((res) => {
                if (res.code == 1) {
                  that.$message({
                    message: "派单成功!",
                    type: "success"
                  })
                  that.load()
                } else {
                  that.$message.error(res.data)
                }
              })
            })
        } else {
          that.$post(isurl, o).then((res) => {
            if (res.code == 1) {
              that.$message({
                message: "派单成功!",
                type: "success"
              })
              that.load()
            } else {
              that.$message.error(res.data)
            }
          })
        }
      }
      if (e.type == "取消发单") {
        o.cancel = 1
        this.$confirm("该订单已发布到派单大厅，您确定要取消发单吗？")
          .then((_) => {
            that.$post(isurl, o).then((res) => {
              if (res.code == 1) {
                that.$message({
                  message: "取消成功!",
                  type: "success"
                })
                that.load()
              } else {
                that.$message.error(res.data)
              }
            })
          })
          .catch((_) => { })
      }
      if (e.type == "发货") {
        that.$post(isurl, o).then((res) => {
          if (res.code == 1) {
            that.$message({
              message: "发货成功!",
              type: "success"
            })
            that.load()
          } else {
            that.$message.error(res.data)
          }
        })
      }
      // setTimeout(function () {
      //   // 关闭 详情页
      //   that.updateUserState()
      // }, 500)
    },
    // 手填订单改变状态
    changeHandOrder(e) {
      let that = this
      let isurl = "/szmb/szmborderstorecontroller/updateorder"
      let o = {
        orderNum: e.order,
        orderStatus: e.state,
        header: "json"
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "操作成功",
            type: "success"
          })
          that.openHandOrder()
        } else {
          that.$message.error(res.data)
        }
      })
    },

    // 更新订单详情里的按钮状态
    updateUserState(flag) {
      let that = this
      let data = that.orderDetailRow
      let isurl = "/szmb/szmborder/orderone"
      let o = {
        orderId: data.orderId
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.orderDetailRow = res.data
          that.orderDetailDialog = !!flag
        } else {
          that.$message.error(res.data)
        }
      })
    },

    goSetDeduct(row) {
      let that = this
      that.$router.push({
        name: "usermanage",
        params: {
          _from: "orderAdmin1",
          userId: row.id,
          orderNum: row.orderNum,
          type: "固定"
        }
      })
    },
    lookUpOperateLog(row) {
      let that = this
      that.operateData.orderNum = row.orderNum
      that.operateData.dialog = true
      let isurl = "/szmb/updateordercontroller/selectall"
      let o = {
        storeId: that.Cookies.get("storeId"),
        orderNum: row.orderNum
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.operateData.list = res.data
        } else {
          that.operateData.list = []
        }
      })
    },

    // 修改订单
    editOrderMakeSure() {
      let that = this
      let data = that.orderDetailData
      let arr1 = []
      let arr2 = []
      if (data.specList) {
        arr1 = data.specList.map((item) => {
          let json = {
            orderDetailsId: item.orderdetailsId,
            orderProductNum: item.standardNumber
          }
          return json
        })
      }
      if (data.shopGroupList) {
        arr2 = data.shopGroupList.map((item) => {
          let json = {
            groupOrderNum: item.orderdetailsId,
            orderProductNum: item.standardNumber
          }
          return json
        })
      }

      let o = {
        userRemark: data.id, // id是个废字段，利用上
        userPhone: data.userPhone,
        userName: data.userName,
        userAddress:
          data.region[0] +
          data.region[1] +
          data.region[2] +
          data.detailedAddress +
          " " +
          (data.userType == 1 ? "公司" : "家庭") +
          "·" +
          (data.islift ? "有电梯" : "无电梯") +
          "·" +
          data.floor +
          "楼",
        orderNum: data.orderNumber,
        smzCOrderDetailsList: arr1, // 普通
        smzCGroupOrderList: arr2,
        userRole: 0,
        header: "json"
      }
      let isurl = "/szmb/updateordercontroller/updateorder"
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "修改成功",
            type: "success"
          })
          that.orderDetailDialog = false
          that.load()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 银行转账-确认收款
    makeSureGetMoney(row) {
      let that = this
      let isurl = "/szmb/user/updatebanktransfer"
      let o = {
        orderNum: row.orderNum,
        state: 1
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "操作成功!",
            type: "success"
          })
          that.load()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 查看资产管理信息
    lookZCinfo(row) {
      console.log("查看资产", row)
      let that = this
      let isurl = "/szmb/szmborder/selbuckInfobyordernum"
      let o = {
        orderNum: row.orderNum
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.AssetsList = res.data
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 撤销押桶
    cancelBucket(row) {
      let that = this
      let isurl = "/szmb/newdebtbuckcontroller/updatedeliveryinfo"
      let o = {
        orderNumber: row.orderNum,
        state: 3
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            type: "success",
            message: "撤销成功"
          })
          that.load()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 确认回收
    confirmHs(row) {
      let that = this
      let isurl = "/szmb/szmstoreapplycontroller/deliveryquern"
      let o = {
        id: row.id,
        source: 0
      }
      that.$post(isurl, o).then((res) => {
        console.log(res)
        if (res.code == 1) {
          that.$message({
            message: "确认成功!",
            type: "success"
          })
          that.lookUpPledBucket1()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    adminback(aa) {
      let that = this
      let isurl = "/szmb/newdebtbuckcontroller/adminback"
      let o = {
        orderNumber: aa,
      }
      that.$post(isurl, o).then((res) => {
        console.log(res)
        if (res.code == 1) {
          that.$message({
            message: "确认成功!",
            type: "success"
          })
          that.lookUpPledBucket1()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 水票记录-中转参数
    goLookUpWaterTicket1(data) {
      let that = this
      that.userDetail.currentData = data
      that.lookUpWaterTicket1()
    },
    // 水票记录-确认到账
    makeSureGetMoney2(row) {
      let that = this
      let isurl = "/szmb/water/affirbank"
      let o = {
        r2: row.orderNum,
        isBankAffirm: 1,
        refund: 1,
        header: "json"
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "确认成功!",
            type: "success"
          })
          that.page = 1;
          that.getticket()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    makeSureGetMoney4(row) {
      let that = this
      let isurl = "/szmb/water/affirbankonly"
      let o = {
        r2: row.orderNum,
        isBankAffirm: 1,
        refund: 1,
        header: "json"
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "确认成功!",
            type: "success"
          })
          that.page = 1;
          that.getticket()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    dongjieshuipiao(row, state) {
      let that = this
      let isurl = "/szmb/water/dongjie"
      let o = {
        orderNum: row.orderNum,
        delState: state
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "冻结成功!",
            type: "success"
          })
          that.page = 1;
          that.getticket()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    shanchushuipiao(id, wcDetailsId) {
      // 弹窗确认
      this.$confirm(`确认删除操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let that = this
        let isurl = "/smzcwcuse/smzcwcuserefunddel"
        let o = {
          wcRelevanceId: id,
          wcDetailsId: wcDetailsId
        }
        that.$post(isurl, o).then((res) => {
          if (res.code == 1) {
            that.$message({
              message: "删除成功!",
              type: "success"
            })
            that.page = 1;
            that.getticket()
          } else {
            that.$message.error(res.data)
          }
        })
      })
    },
    confirmRefund(row, state) {
      let that = this
      let isurl = "/smzcwcuse/refundConfirm"
      let o = {
        wcRelevanceId: row.reId,
        flag: state
      }
      this.$confirm(`确认退款操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        that.$post(isurl, o).then((res) => {
          if (res.code == 1) {
            that.$message({
              message: "退款成功!",
              type: "success"
            })
            that.page = 1;
            that.getticket()
          } else {
            that.$message.error(res.data)
          }
        })
      })
    },
    makeSureGetMoney3(row) {
      let that = this
      let isurl = "/szmb/water/affirbank"
      let o = {
        r2: row.orderNum,
        isBankAffirm: 1,
        refund: 2,
        header: "json"
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.$message({
            message: "确认成功!",
            type: "success"
          })
          that.page = 1;
          that.getticket()
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 单个发
    sendMsgMethod2(row) {
      let that = this
      that.newDemandMsgVisible2 = true
      that.choiceUserList2 = row.name ? row.name : "无地址客户"
      that.userIDList = [row.userId]
      that.showUserListType2 = 1
    },
    // 单个发
    sendMsgMethod(row) {
      let that = this
      that.newDemandMsgVisible = true
      that.choiceUserList = row.name ? row.name : "无地址客户"
      that.userIDList = [row.userId]
      that.showUserListType = 1
    },
    editUser(index, scope) {
      let that = this
      that.userFormData.userId = scope.id
      that.editLoad()
      that.getClassType(scope.id)
    },

    editLoad() {
      let that = this
      that.editDiscountLoad()
      let isurl = "/szmb/user/selectone"
      let o = {
        storeId: that.Cookies.get("storeId"),
        userId: that.userFormData.userId
      }
      that.$post(isurl, o).then((res) => {
        console.log(res)
        if (res.code === 1) {
          that.userFormData.name = res.data.name
          that.userFormData.nickName = res.data.nickName
          that.userFormData.phone = res.data.phone
          that.userFormData.landLine = res.data.newPhone
          that.userFormData.companyName = res.data.companyName
          that.userFormData.region = [
            res.data.province,
            res.data.city,
            res.data.area
          ]
          that.userFormData.address = res.data.detailedAddress
          that.userFormData.addressId = res.data.addressId
          that.userFormData.isLift = res.data.stairs == 1
          that.userFormData.floorNum = Number(res.data.buildingNo)
          that.userFormData.accountIndex = Number(res.data.type)
          that.userFormData.upFloorMoney = res.data.upFloorMoney
          that.userFormData.businessFlag = res.data.state
          that.userFormData.businessTc = res.data.business
          that.userFormData.upFloorTc = res.data.comeUpFee
          that.userFormData.distanceTc = res.data.distanceFee
          that.userFormData.singleTc = res.data.numberFee
          that.userFormData.isOpen = !!res.data.isOpen
          that.userFormData.day = res.data.day
          that.userFormData.count = res.data.count
          // that.userFormData.qx = !!res.data.qx
          // that.userFormData.jw = !!res.data.jw
          // that.userFormData.yf = !!res.data.yf
          // that.userFormData.switchList = res.data.userSwitchVo
          that.userFormData.functionFlag = res.data.status ? 1 : 0
          that.userFormData.servicePrice = res.data.servicePrice
            ? res.data.servicePrice
            : "0.00"
          that.userFormData.discountGoods = res.data.userAndProduceList
            ? res.data.userAndProduceList.list
            : []
          that.waterDiscountData.waterChecked = res.data.waterDiscountsList
            ? res.data.waterDiscountsList
            : []

          that.userFormData.lastGoodsList = res.data.lastOrder
            ? res.data.lastOrder.specList
              ? res.data.lastOrder.specList
              : res.data.lastOrder.shopGroupList
            : []

          if (res.data.status) {
            that.userFormData.switchList = res.data.userSwitchVo
          } else {
            that.userFormData.switchList = [
              {
                alias: "qx",
                moduleName: "清洗服务",
                servePrice: 0,
                state: 0,
                userSwitchId: 0,
                content: "打开后，客户可见此功能，即可使用"
              },
              {
                alias: "jw",
                moduleName: "借物功能",
                servePrice: null,
                state: 0,
                userSwitchId: 0,
                content: "打开后，客户可见此功能，即可借物"
              },
              {
                alias: "yf",
                moduleName: "月付功能",
                servePrice: null,
                state: 0,
                userSwitchId: 0,
                content: "打开后，客户可见此功能，即可月结"
              },
              {
                alias: "yhzz",
                moduleName: "银行转账",
                servePrice: null,
                state: 0,
                userSwitchId: 0,
                content: "打开后，客户可见此功能，即可银行转账"
              },
              {
                alias: "pp",
                moduleName: "普票开关",
                servePrice: null,
                state: 0,
                userSwitchId: 0,
                content: "打开后，客户可见此功能，即可申请开普票"
              },
              {
                alias: "zp",
                moduleName: "专票开关",
                servePrice: null,
                state: 0,
                userSwitchId: 0,
                content: "打开后，客户可见此功能，即可申请开专票"
              },
              {
                alias: "gmsp",
                moduleName: "购买水票",
                servePrice: null,
                state: 1,
                userSwitchId: 0,
                content: "打开后，客户可见此功能，即可购买水票"
              },
              {
                alias: "xsyt",
                moduleName: "线上押桶",
                servePrice: null,
                state: 1,
                userSwitchId: 0,
                content: "打开后，客户可见此功能，即可线上押桶"
              },
              {
                alias: "ytjl",
                moduleName: "押桶记录",
                servePrice: null,
                state: 1,
                userSwitchId: 0,
                content: "打开后，客户可见此功能，即可线上押桶"
              }
            ]
          }
          console.log('编辑用户', that.userFormData)
          that.addUserModal = true
        } else {
          that.$message.error(res.data)
        }
      })
    },
    editDiscountLoad() {
      let that = this
      let isurl = "szmb/user/userwater"
      let o = {
        userId: that.userFormData.userId
      }
      that.$post(isurl, o).then((res) => {
        if (res.code == 1) {
          that.userFormData.discountGoodsList = res.data
        } else {
          that.$message.error(res.data)
        }
      })
    },
    getClassType(id, fun) {
      let that = this
      let isurl = "/szmb/szmbvippricecontroller/selectuserproduct"
      let o = {
        userId: id,
        storeId: that.Cookies.get("storeId"),
        shopClassId: 0,
        state: 0
      }
      that.$post(isurl, o).then((res) => {
        console.log(res)
        if (res.code == 1) {
          let list = res.data.shopClasses
          let newList = list.filter(function (item) {
            return item.classId != 6 && item.classId != 7
          })
          that.discountData.classifyList = newList
          that.discountData.classifyId = newList[0].classId
          that.discountData.classifyName = newList[0].name
          typeof fun == "function" && fun(res)
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 查看详情
    lookDetail(item) {
      console.log(item)
      this.$router.push({
        name: "usermanage",
        // params: {
        //   _from: "orderAdmin",
        //   userId: item.id,
        //   orderNum: "",
        //   type: "固定"
        // }
        params: { type: "msg", userId: item.id }
      })
    },
    DeselectTem() {
      this.newMsgInnder = false
      this.newDemandForm.msgTemplate = ""
    },
    // 确认选择
    deleSelection() {
      if (this.newDemandForm.msgTemplate == "") {
        this.$message({
          type: "warning",
          message: "请选择模板"
        })
      } else {
        this.newMsgInnder = false
      }
    },
    cancelSendMsg() {
      let that = this
      that.newDemandMsgVisible = false
      that.newDemandForm = {
        newMsgType: 0,
        msgTemplate: "",
        msgTitle: "",
        msgContent: "",
        saveMsgTem: false,
        startTime: "",
        endTime: ""
      }
    },
    sendNewMsgGo() {
      let that = this
      if (that.newDemandForm.msgTitle.trim() == "") {
        that.$message({
          type: "warning",
          message: "请输入消息名称"
        })
        return
      }
      if (that.newDemandForm.msgContent.trim() == "") {
        that.$message({
          type: "warning",
          message: "请输入消息内容"
        })
        return
      }
      if (that.newDemandForm.newMsgType == 1) {
        if (
          !(
            that.newDemandForm.startTime != "" &&
            that.newDemandForm.endTime != ""
          )
        ) {
          that.$message({
            type: "warning",
            message: "请选择时间"
          })
          return
        }
      }

      let oData = {
        storeId: that.Cookies.get("storeId"),
        startTime: that.newDemandForm.startTime,
        endTime: that.newDemandForm.endTime,
        msgId: that.newDemandForm.newMsgType, // 0tz 1hd
        msgInfo: that.newDemandForm.msgContent, // 消息内容
        templateName: that.newDemandForm.msgTitle, // 消息名称
        saveTemplate: that.newDemandForm.saveMsgTem, // 是都保存模板
        userId: that.userIDList,
        templateId: that.newDemandForm.msgTemplate,
        userType: that.showUserListType == 3 ? 0 : 1, // 0群发 1单发
        header: "json"
      }

      console.log(oData, "发送消息数据")
      that.sendMsgOrSaveTemplateApi(oData)
    },
    // 新增消息接口
    sendMsgOrSaveTemplateApi(oData) {
      var that = this
      let oUrl = "/szmb/msg/insertmsgapp"
      that
        .$post(oUrl, oData)
        .then((res) => {
          if (res.code == 1) {
            that.$message({
              type: "success",
              message: "发送成功"
            })
            that.cancelSendMsg()
          } else if (res.code == 0) {
            that.$message({
              type: "warning",
              message:
                "消息发送成功。已达消息模板上限条数30条，此模板保存不成功。"
            })
            that.cancelSendMsg()
          } else if (res.code == 2) {
            that.$message.error(res.data)
          } else {
            console.log(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 获取消息模板列表
    getMsgTemplateApi() {
      var that = this
      let oUrl = "/szmb/msgTemplate/getMsgTemplateByMerchantId"
      let oData = {
        merchantId: that.Cookies.get("storeId"),
        templateType: that.newDemandForm.newMsgType,
        header: "json"
      }
      that
        .$post(oUrl, oData)
        .then((res) => {
          if (res.code == 1) {
            that.msgTempList = res.data
          } else {
            that.msgTempList = []
            console.log(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 修改消息模板
    editMsgTempalte(row) {
      let that = this
      console.log(row)
      that.newMsgThreeInnder = true
      that.editMsgTemForm = {
        editMsgTitle: row.templateName,
        editMsgCont: row.templateContent,
        templateId: row.templateId,
        templateType: row.templateType
      }
    },
    // 保存修改消息
    saveEditMsgTem() {
      var that = this
      let oUrl = "/szmb/msgTemplate/updateMsgTemplate"
      let oData = {
        merchantId: that.Cookies.get("storeId"),
        templateContent: that.editMsgTemForm.editMsgCont,
        templateId: that.editMsgTemForm.templateId,
        templateName: that.editMsgTemForm.editMsgTitle,
        templateType: that.editMsgTemForm.templateType,
        header: "json"
      }
      that
        .$post(oUrl, oData)
        .then((res) => {
          if (res.code == 1) {
            that.$message({
              type: "success",
              message: "保存成功"
            })
            that.getMsgTemplateApi()
            that.newMsgThreeInnder = false
          } else {
            console.log(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 删除消息模板
    deleteMsgTemApi(templateId, otype) {
      var that = this
      this.$confirm("确认删除此模板?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          let oUrl = "/szmb/msgTemplate/offlineMsgTemplate/" + templateId
          this.$get(oUrl)
            .then(function (res) {
              if (res.code === 1) {
                that.$message({
                  type: "success",
                  message: "删除成功"
                })
                otype == 1
                  ? that.getMsgTemplateApi()
                  : that.getMsgTemplateApi2()
              } else {
                that.$message.error(res.data)
              }
            })
            .catch(function (err) {
              console.log(err)
            })
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除"
          })
        })
    },

    // new demand new message end
    // new demand new short message begin =====================================
    DeselectTem2() {
      this.newMsgInnder2 = false
      this.newDemandForm2.msgTemplate = ""
    },
    // 确认选择
    deleSelection2() {
      if (this.newDemandForm2.msgTemplate == "") {
        this.$message({
          type: "warning",
          message: "请选择模板"
        })
      } else {
        this.newMsgInnder2 = false
      }
    },
    cancelSendMsg2() {
      let that = this
      that.newDemandMsgVisible2 = false
      that.newDemandForm2 = {
        newMsgType: 4,
        msgTemplate: "",
        msgTitle: "",
        msgContent: "",
        saveMsgTem: false
      }
    },
    sendNewMsgGo2() {
      let that = this
      if (that.newDemandForm2.msgTitle.trim() == "") {
        that.$message({
          type: "warning",
          message: "请输入短信名称"
        })
        return
      }
      if (that.newDemandForm2.msgContent.trim() == "") {
        that.$message({
          type: "warning",
          message: "请输入短信内容"
        })
        return
      }
      let oData = {
        storeId: that.Cookies.get("storeId"),
        msgId: that.newDemandForm2.newMsgType, // 4tz 5hd
        msgInfo: that.newDemandForm2.msgContent, // 消息内容
        templateName: that.newDemandForm2.msgTitle, // 消息名称
        saveTemplate: that.newDemandForm2.saveMsgTem, // 是都保存模板
        userId: that.userIDList,
        templateId: that.newDemandForm2.msgTemplate,
        userType: that.showUserListType2 == 3 ? 0 : 1, // 0群发 1单发
        header: "json"
      }
      that.sendMsgOrSaveTemplateApi2(oData)
    },
    // 新增短信
    sendMsgOrSaveTemplateApi2(oData) {
      var that = this
      let oUrl = "/szmb/msg/insertmsgapp"
      that
        .$post(oUrl, oData)
        .then((res) => {
          if (res.code == 1) {
            that.$message({
              type: "success",
              message: "发送成功"
            })
            that.cancelSendMsg2()
          } else if (res.code == 0) {
            that.$message({
              type: "warning",
              message:
                "消息发送成功。已达消息模板上限条数30条，此模板保存不成功。"
            })
            that.cancelSendMsg2()
          } else if (res.code == 2) {
            that.$message.error(res.data)
          } else {
            console.log(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 获取短信模板列表
    getMsgTemplateApi2() {
      var that = this
      let oUrl = "/szmb/msgTemplate/getMsgTemplateByMerchantId"
      let oData = {
        merchantId: that.Cookies.get("storeId"),
        templateType: that.newDemandForm2.newMsgType,
        header: "json"
      }
      that
        .$post(oUrl, oData)
        .then((res) => {
          if (res.code == 1) {
            that.msgTempList2 = res.data
          } else {
            that.msgTempList2 = []
            console.log(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // 修改短信模板
    editMsgTempalte2(row) {
      let that = this
      console.log(row)
      that.newMsgThreeInnder2 = true
      that.editMsgTemForm2 = {
        editMsgTitle: row.templateName,
        editMsgCont: row.templateContent,
        templateId: row.templateId,
        templateType: row.templateType
      }
    },
    // 保存修改消息
    saveEditMsgTem2() {
      var that = this
      let oUrl = "/szmb/msgTemplate/updateMsgTemplate"
      let oData = {
        merchantId: that.Cookies.get("storeId"),
        templateContent: that.editMsgTemForm2.editMsgCont,
        templateId: that.editMsgTemForm2.templateId,
        templateName: that.editMsgTemForm2.editMsgTitle,
        templateType: that.editMsgTemForm2.templateType,
        header: "json"
      }
      that
        .$post(oUrl, oData)
        .then((res) => {
          if (res.code == 1) {
            that.$message({
              type: "success",
              message: "保存成功"
            })
            that.getMsgTemplateApi2()
            that.newMsgThreeInnder2 = false
          } else {
            console.log(res.msg)
          }
        })
        .catch((err) => {
          console.log(err)
        })
    },
    // new demand new short message end

    // new add demand Remove offer settings begin
    otherRemoveUprice(row) {
      console.log("remove prices", row)
      let that = this
      let isurl = "/szmb/user/removePreferentialPrice"
      let o = {
        userId: that.userFormData.userId,
        skuId: row.skuId
      }
      that.$post(isurl, o).then(function (res) {
        if (res.code == 1) {
          that.$message({
            message: "移除成功",
            type: "success"
          })
          that.setDiscount(that.userFormData.userId)
        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 移除
    openRemoveDialog(row) {
      let that = this
      that.removeSkuId = row.skuId
      that.removeDialog = true
    },
    makeSureToRemove() {
      let that = this
      let isurl = "/szmb/user/deldiscount"
      let o = {
        userId: that.userFormData.userId,
        skuId: that.removeSkuId,
        state: that.removeWhich
      }
      that.$post(isurl, o).then(function (res) {
        if (res.code == 1) {
          that.$message({
            message: "移除成功",
            type: "success"
          })
          that.removeDialog = false
          that.removeSkuId = ""
          that.removeWhich = "0"
          that.setDiscount(that.userFormData.userId)
        } else {
          that.$message.error(res.data)
        }
      })
    },
    setDiscount(id) {
      let that = this
      let isurl = "/szmb/szmbvippricecontroller/selectuserproduct"
      let o = {
        userId: id,
        storeId: that.Cookies.get("storeId"),
        shopClassId: that.discountData.classifyId,
        state: that.discountData.classifyState
      }
      that.$post(isurl, o).then((res) => {
        console.log(res)
        if (res.code == 1) {
          let list = res.data.shopClasses
          let newList = list.filter(function (item) {
            return item.classId != 6 && item.classId != 7
          })
          // let tabs = [{ id: 0, name: "全部", classId: 0 }]
          // let tabArr = tabs.concat(newList)
          // that.discountData.classifyList = tabArr
          that.discountData.classifyList = newList
          that.discountData.list = res.data.list

          that.discountDialog = true
        } else {
          that.$message.error(res.data)
        }
      })
    },
    closeRemoveDialog() {
      let that = this
      that.discountDialog = false
      that.editDiscountLoad()
    },
    // 客户编辑提交
    addUserSubmit() {
      let that = this
      this.$refs.addUserElement.validate((valid) => {
        if (valid) {
          var data = that.userFormData
          let isurl = ""
          if (data.userId) {
            isurl = "/szmb/user/updatetuser"
          } else {
            isurl = "/szmb/user/insertuser"
          }

          if (!data.name) {
            that.$message.error("请填写姓名")
            return
          }

          if (!data.landLine) {
            if (!data.phone) {
              that.$message.error("请填写手机号")
              return
            }
          }

          if (data.accountIndex == 1) {
            if (!data.companyName) {
              that.$message.error("请输入公司名称")
              return
            }
          }

          if (data.region.length == 0) {
            that.$message.error("请选择区域")
            return
          }

          if (!data.address) {
            that.$message.error("请填写详细地址")
            return
          }

          if (data.isOpen) {
            if (data.day <= 0) {
              that.$message.error("提醒间隔必须大于0天")
              return
            }
          }

          let o = {
            storeId: that.Cookies.get("storeId"),
            name: data.name,
            nickName: data.nickName,
            phone: data.phone,
            companyName: data.accountIndex == 1 ? data.companyName : "",
            province: data.region[0],
            city: data.region[1],
            area: data.region[2],
            detailedAddress: data.address,
            addressId: data.addressId,
            type: data.accountIndex,
            stairs: data.isLift ? 1 : 0,
            buildingNo: data.floorNum,
            lonAndLat: "",
            upFloorMoney: data.upFloorMoney ? data.upFloorMoney : 0,
            blackList: 0, // 状态 冻结 或正常
            userId: data.userId,
            comeUpFee: data.upFloorTc ? data.upFloorTc : 0,
            distanceFee: data.distanceTc ? data.distanceTc : 0,
            numberFee: data.singleTc ? data.singleTc : 0,
            business: data.businessTc ? data.businessTc : 0,
            userSwitchVo: data.switchList,
            isOpen: data.isOpen ? 1 : 0,
            day: data.day ? data.day : 0,

            // qx: data.qx ? 1 : 0,
            // jw: data.jw ? 1 : 0,
            // yf: data.yf ? 1 : 0,

            status: data.functionFlag,
            // servicePrice: data.servicePrice,
            // userAndProduceList: {
            //   list: data.discountGoods,
            //   count: data.count
            // },
            // waterDiscounts: JSON.stringify(data.waterDiscounts),
            header: "json"
          }
          if (!data.upFloorTc && !data.distanceTc && !data.singleTc) {
            this.$confirm("配送提成未设置，确认继续保存操作吗?", "温馨提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                that.$post(isurl, o).then((res) => {
                  console.log(res)
                  if (res.code === 1) {
                    that.load()
                    that.addUserModal = false
                    that.$message({
                      message: "操作成功！",
                      type: "success"
                    })
                    that.$refs.addUserElement.resetFields()

                    // 订单页面跳转指派
                    if (data.userId) {
                      if (that.orderDriver.flag) {
                        if (that.$route.params.type == "固定") {
                          that.openOrderDriverList()
                        } else {
                          that.temporaryGetOrder()
                        }
                      }
                    }
                  } else {
                    that.$message.error(res.data)
                  }
                })
              })
              .catch(() => { })
          } else {
            that.$post(isurl, o).then((res) => {
              console.log(res)
              if (res.code === 1) {
                that.load()
                that.addUserModal = false
                that.$message({
                  message: "操作成功！",
                  type: "success"
                })
                that.$refs.addUserElement.resetFields()
                // 订单页面跳转指派
                if (data.userId) {
                  if (that.orderDriver.flag) {
                    if (that.$route.params.type == "固定") {
                      that.openOrderDriverList()
                    } else {
                      that.temporaryGetOrder()
                    }
                  }
                }
              } else {
                that.$message.error(res.data)
              }
            })
          }
        } else {
          // that.$message.error("")
          return false
        }
      })
    },

    // 订单跳转页面 固定送水员接单
    openOrderDriverList() {
      let that = this
      that.orderDriver.formalDriverList = [] // 固定送水员列表
      that.orderDriver.thisFormalDriver = "" // 选中的固定送水员
      let isurl = "/szmb/szmsendmembercontroller/selectdelectstate"
      let o = {
        storeId: that.Cookies.get("storeId")
      }
      that.$post(isurl, o).then((res) => {
        console.log(res)
        if (res.code == 1) {
          that.orderDriver.formalDriverList = res.data
          that.orderDriver.formalDriverDialog = true
        } else {
          that.$message.error("请先添加送水员")
        }
      })
    },
    // 订单跳转页面 临时送水员接单
    temporaryGetOrder() {
      let that = this
      let isurl = "/szmb/szmborder/updatestate"
      let o = {
        orderId: that.orderDriver.orderId,
        orderStatus: that.orderDriver.orderStatus,
        state: that.orderDriver.state
      }
      that.$post(isurl, o).then((res) => {
        console.log(res)
        if (res.code == 1) {
          that.$message({
            message: "临时送水员指派成功!",
            type: "success"
          })
          that.orderDriver = {
            flag: false,
            formalDriverDialog: false, // 固定送水员dialog
            formalDriverList: [], // 固定送水员列表
            thisFormalDriver: "", // 选中的固定送水员
            orderNum: "", // 当前的订单
            orderId: "", // 当前订单ID
            orderStatus: "",
            state: ""
          }
        } else {
          that.$message.error(res.data)
        }
      })
    },
    makeSureDiscount(row) {
      let that = this
      let buy = row.buy
      let send = row.send
      let waterPrice = row.waterprice
      if (!Number(row.userPrice)) {
        that.$message.error("必须设置优惠价格")
        return
      }

      let discounts = "";
      let waterDiscounts = "";
      if (row.chooseListIndex == 0) {
        // 单品价格
        if (!row.userPrice) {
          this.$message.error("请输入单品优惠价格");
          return false;
        }
        discounts = {
          productId: row.skuId,
          type: 0,
          price: row.userPrice
        };
      } else if (row.chooseListIndex == 1) {
        // 水票价格无赠送
        if (!row.userPrice || !row.buy) {
          this.$message.error("请输入水票优惠价格，和数量");
          return false;
        }
        waterDiscounts = {
          productId: row.skuId,
          type: 1,
          price: row.userPrice,
          presented: row.buy + ",0"
        };
      } else if (row.chooseListIndex == 2) {
        // 水票价格有赠送
        if (!row.userPrice || !row.buy || row.send == null) {
          this.$message.error("请输入水票优惠价格，和数量");
          return false;
        }
        waterDiscounts = {
          productId: row.skuId,
          type: 2,
          price: row.userPrice,
          presented: row.buy + "," + row.send
        };
      }
      // if (Number(row.buy) > 0 && Number(row.send) > 0) {
      //   discounts = {
      //     productId: row.skuId,
      //     price: row.userPrice
      //   }
      //   waterDiscounts = {
      //     productId: row.skuId,
      //     price: row.waterprice,
      //     presented: row.buy + "," + row.send
      //   }
      //   that.ooblooer = true
      // } else {
      //   discounts = {
      //     productId: row.skuId,
      //     price: row.userPrice
      //   }
      //   waterDiscounts = {
      //     productId: row.skuId,
      //     price: '',
      //     presented: ''
      //   }
      //   that.ooblooer = false
      // }
      let isurl = "/szmb/user/updatediscount"
      let o
      // if (that.ooblooer) {
      //   o = {
      //     userId: that.userFormData.userId,
      //     discounts: JSON.stringify(discounts),
      //     waterDiscounts: JSON.stringify(waterDiscounts)
      //   }
      // } else {
      //   o = {
      //     userId: that.userFormData.userId,
      //     discounts: JSON.stringify(discounts),
      //     waterDiscounts: JSON.stringify(waterDiscounts)
      //   }
      // }
      if (discounts != "") {
        o = {
          userId: that.userFormData.userId,
          discounts: JSON.stringify(discounts),
        }
      }

      if (waterDiscounts != "") {
        o = {
          userId: that.userFormData.userId,
          waterDiscounts: JSON.stringify(waterDiscounts)
        }
      }

      that.$post(isurl, o).then(function (res) {
        if (res.code == 1) {
          that.$message({
            type: "success",
            message: "保存成功！"
          })
          that.setDiscount(that.userFormData.userId)
        } else {
          that.$message.error(res.data)
        }
      })
    },

    // new add demand Remove offer settings begin
    otherRemoveUprice(row) {
      let that = this
      let isurl = "/szmb/user/removePreferentialPrice"
      let o = {
        userId: that.userFormData.userId,
        skuId: row.skuId
      }
      that.$post(isurl, o).then(function (res) {
        if (res.code == 1) {
          that.$message({
            message: "移除成功",
            type: "success"
          })
          that.setDiscount(that.userFormData.userId)
        } else {
          that.$message.error(res.data)
        }
      })
    },
    printHandler(e) {
      this.indexOrder = e;

      let that = this
      let isurl = "/szmcordermaincontroller/updateother"
      let o = {
        orderMainId: e.orderId,
        isprint: 1,
        header: "json"
      }
      that.$post(isurl, o).then(function (res) {
        if (res.code == 1) {
          that.$message({
            message: "打印成功",
            type: "success"
          })

          setTimeout(function () {
            const style = "@page {margin:0 10mm};"; //打印时去掉眉页眉尾
            print({
              printable: "printBill", // 备注元素id
              type: "html",
              header: "",
              targetStyles: ["*"],
              scanStyles: false, //打印必须加上，不然页面上的css样式无效
              style,
            });
            that.load();
          }, 500);

        } else {
          that.$message.error(res.data)
        }
      })
    },
    // 打印顺丰单号
    printSfExpress(row) {
      // 创建顺丰订单
      this.$get('/szmb/szmborder/sfexpress/createorderfromid', {
        orderId: row.orderId,
      }).then((res) => {
        if (res.code == 1) {
          // 创建顺丰订单成功，继续打印面单
          this.printSfWaybill(row);
        } else {
          this.$message.error(res.msg || '创建顺丰订单失败')
        }
      }).catch((error) => {
        console.error('创建顺丰订单失败:', error);
        this.$message.error('创建顺丰订单失败');
      })
    },
    // 打印顺丰面单
    printSfWaybill(row) {
      var that = this;
      this.$get('/szmb/szmborder/sfexpress/printwaybillbyorderid', {
        orderId: row.orderNum,
      }).then((res) => {
        if (res.code == 1) {
          // 获取打印面单成功，处理文件下载
          const data = res.data;
          if (data && data.fileUrls && data.fileUrls.length > 0) {
            // 获取原始响应中的文件信息
            const originalResponse = data.originalResponse;
            if (originalResponse && originalResponse.obj && originalResponse.obj.files) {
              // 遍历所有文件进行下载
              originalResponse.obj.files.forEach((file, index) => {
                if (file.url && file.token) {
                  this.downloadSfWaybillPdf(file.url, file.token, `顺丰面单_${row.orderNum}_${index + 1}.pdf`);
                }
              });

            } else {
              // 如果没有原始响应，使用简化的URL列表
              data.fileUrls.forEach((url, index) => {
                // 注意：这种情况下没有token，可能无法下载
                this.$message.warning(`文件${index + 1}缺少授权token，无法下载`);
              });
            }
            that.load()
          } else {
            this.$message.warning('没有找到可下载的面单文件');
          }
        } else {
          this.$message.error(res.msg || '打印面单请求失败')
        }
      }).catch((error) => {
        console.error('打印面单请求失败:', error);
        this.$message.error('打印面单请求失败');
      })
    },
    // 下载顺丰面单PDF文件
    downloadSfWaybillPdf(url, token, filename) {
      // 创建一个临时的a标签来下载文件
      const link = document.createElement('a');
      link.style.display = 'none';

      // 使用fetch下载文件，添加授权头
      fetch(url, {
        method: 'GET',
        headers: {
          'X-Auth-Token': token
        }
      }).then(response => {
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.blob();
      }).then(blob => {
        // 创建blob URL
        const blobUrl = window.URL.createObjectURL(blob);
        link.href = blobUrl;
        link.download = filename;

        // 添加到DOM并触发下载
        document.body.appendChild(link);
        link.click();

        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(blobUrl);

        this.$message.success(`顺丰面单文件 ${filename} 下载成功`);
      }).catch(error => {
        console.error('下载顺丰面单文件失败:', error);
        this.$message.error(`下载顺丰面单文件失败: ${error.message}`);
      });
    },
    // 催单发货
    urgeStore(item) {
      const that = this;
      const orderNum = item.orderNum;

      if (!orderNum) {
        this.$message.error('订单号不能为空');
        return;
      }

      this.$confirm('确认催单发货？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 显示加载状态
        const loading = this.$loading({
          lock: true,
          text: '催单中...',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 调用催单发货API
        this.$post('/szmcuercontroller/urgestore', {
          orderNum: orderNum
        }).then((res) => {
          loading.close();

          if (res.code == 1) {
            this.$message.success('催单成功');
            // 刷新订单列表
            setTimeout(() => {
              this.load();
            }, 1000);
          } else {
            this.$message.error(res.data || '催单失败');
          }
        }).catch((error) => {
          loading.close();
          console.error('催单发货请求失败:', error);
          this.$message.error('催单发货请求失败');
        });
      }).catch(() => {
        // 用户取消操作
      });
    },
    openOrderLocation(item) {
      console.log(item)
      if (item.lon && item.lat) {
        // this.longitude = item.lon
        // this.latitude = item.lat
        // 得做坐标转换
        // 火星坐标系(GCJ02)转WGS84
        const gcj02Point = { lng: item.lon, lat: item.lat };
        const wgs84Point = gcj02ToWgs84(gcj02Point.lng, gcj02Point.lat);
        console.log(wgs84Point); // 输出转换后的WGS84坐标

        this.longitude = wgs84Point.lng
        this.latitude = wgs84Point.lat

        this.locationTitle = item.underway
        this.orderlocationVisible = true
      } else {
        this.$message.error("暂无位置信息")
      }

    },
    // 处理全选
    handleCheckAllChange(val) {
      this.list.forEach(item => {
        item.isChecked = val;
        if (val) {
          if (!this.selectedOrders.includes(item.orderNum)) {
            this.selectedOrders.push(item.orderNum);
          }
        } else {
          this.selectedOrders = [];
        }
      });
    },

    // 处理单个复选框变化
    handleCheckChange(val, item) {
      if (val) {
        if (!this.selectedOrders.includes(item.orderNum)) {
          this.selectedOrders.push(item.orderNum);
        }
      } else {
        const index = this.selectedOrders.indexOf(item.orderNum);
        if (index > -1) {
          this.selectedOrders.splice(index, 1);
        }
      }

      // 检查是否全部选中
      this.checked = this.list.every(item => item.isChecked);
    },
    // 处理picurl字符串，分割成数组
    getPicUrlArray(picurl) {
      if (!picurl || typeof picurl !== 'string') {
        return []
      }
      // 使用逗号分割字符串，并过滤掉空字符串
      return picurl.split(',').filter(url => url.trim() !== '')
    },
    // 预览图片
    previewImages(imageList, index) {
      this.previewImageList = imageList
      this.currentImageIndex = index
      this.imagePreviewVisible = true
    },
    // 处理图片加载错误
    handleImageError(event) {
      // 设置默认图片或隐藏图片
      event.target.style.display = 'none'
    }
  },
}
</script>

<style lang="scss" scoped>
.goodsCard {
  width: 100%;
  padding: 0 15px;
  box-sizing: border-box;
  padding-bottom: 20px;
}

.goodsCard>div:nth-child(1) {
  width: 30%;
}

.goodsCard>div:nth-child(2) {
  width: 70%;
}

::v-deep.el-radio__inner {
  width: 20px;
  height: 20px;
}

// .el-table__header-wrapper .el-checkbox {
//   display: none;
// }
.productList ::v-deep.has-gutter .el-checkbox__input .el-checkbox__inner {
  display: none;
}

// .orderDetailDialog ::v-deep.el-form-item--small.el-form-item {
//   margin-bottom: 0px;
// }
// .orderDetailDialog ::v-deep.el-dialog__body {
//   padding: 0px 20px;
// }

//资产管理
.zcgl-flex-box {
  display: flex;
  justify-content: flex-start;

  .zcgl-box-cent {
    width: calc(50% - 1px);
    padding: 10px 20px;
    box-sizing: border-box;
  }

  .zcgl-line-line {
    width: 1px;
    background: #d4d3d3;
  }
}

.el-form-item-flex {
  display: flex;
  justify-content: flex-start;

  .el-form-item {
    p {
      margin: 0;

      span:not(:first-child) {
        margin-left: 8px;
      }
    }
  }
}

.back-tong-cont {
  margin: 0 !important;
}

::v-deep .el-icon-back:before {
  content: "" !important;
}

::v-deep .radio-selected {
  color: #1693fd;
}

// .moneyBox{
//   width: 400px;
// }
.moneyBox>div {
  width: 250px;
  margin-right: 50px;
}

.top-box {
  padding: 10px 20px;
  background: rgba(239, 242, 247, 1);
  border: 1px solid rgba(216, 220, 229, 1);
  border-right: none;
  text-align: center;
  font-size: 14px;
  // box-sizing: border-box;
}

.active_top_box {
  background: rgba(255, 255, 255, 1);
  color: #1693fd;
  border-bottom: 1px solid transparent;
}

.content_top {
  width: 100%;
  height: 40px;
  background: rgba(239, 242, 247, 1);
  margin-top: 15px;
  border: 1px solid rgba(216, 220, 229, 1);
  box-sizing: border-box;
}

.content_top>div {
  height: 100%;
  border-right: 1px solid rgba(216, 220, 229, 1);
  display: flex;
  align-items: center;
  justify-content: center;
}

.article {
  margin-top: 15px;
  border: 1px solid rgba(216, 220, 229, 1);
  font-size: 16px;
}

.article_top {
  width: 100%;
  min-height: 40px;
  line-height: 40px;
  background: rgba(239, 242, 247, 1);
  border-bottom: 1px solid rgba(216, 220, 229, 1);
  font-size: 14px;
  padding: 0 20px;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  // justify-content: space-between;
}

.article_content {
  width: 100%;
  border-right: 1px solid rgba(216, 220, 229, 1);
  display: flex;
}

.article_content>div {
  border-right: 1px solid rgba(216, 220, 229, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
  flex-wrap: wrap;
}

.article_content_two {
  width: 349px;
}

.article_content_two>div {
  line-height: 26px;
}

.operate>div {
  cursor: pointer;
  box-sizing: border-box;
}

.operate>div:hover {
  border-top: 1px solid #1693fd;
  border-bottom: 1px solid #1693fd;
}

.tapMenuBadge {
  width: 20px;
  height: 20px;
  box-sizing: border-box;
  border-radius: 50%;
  background: rgba(239, 18, 18, 1);
  font-size: 10px;
  color: white;
  text-align: center;
  line-height: 20px;
  position: absolute;
  top: -10px;
  right: -10px;
  z-index: 100;
}

.imgBoxFlag {
  position: absolute;
  top: 0;
  left: 0;
}

// .popperClass {
//   background: rgba(61, 76, 102, 0.8) !important;
//   color: white !important;
// }
::v-deep.el-popper ::v-deep.popper__arrow::after {
  bottom: -6px;
  left: 1px;
  border-right-color: black;
  border-left-width: 0;
}

.urge-icon {
  width: 150px;
  height: 38px;
  padding-left: 20px;
  background: #DC322B;
  background: -moz-linear-gradient(left, #DC322B, white);
  background: -webkit-linear-gradient(left, #DC322B, white);
  background: -ms-linear-gradient(left bottom, #DC322B, white);
  background: linear-gradient(left, #DC322B, white);
  -ms-filter: "progid:DXImageTransform.Microsoft.gradient (GradientType=0, startColorstr=#DC322B, endColorstr=white)";
  line-height: 38px;
}

.check123 {
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #1895fd;
  color: white;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
}
.mark-tag {
  height: 30px;
  line-height: 30px;
  text-align: center;
  background: #1895fd;
  color: white;
  border-radius: 8px;
  text-align: center;
  font-size: 12px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  padding: 0 10px;
  margin-right: 10px;
}

.nocheck {
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  color: #1895fd;
  border: 1px solid #1895fd;
  border-radius: 8px;
  text-align: center;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
}

.lianying {
  position: fixed;
  bottom: 100px;
  right: 0;
  width: 120px;
  height: 120px;
  background-color: #1794fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-direction: column;
  font-size: 32px;
  font-weight: 600;
  z-index: 9999;
  line-height: 36px;
}

// 图片相关样式
.pic-container {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.pic-thumbnail {
  width: 30px;
  height: 30px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;

  &:hover {
    transform: scale(1.1);
    border-color: #409eff;
  }
}

.pic-more {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 2px 4px;
  border-radius: 2px;
  white-space: nowrap;
}

.image-preview-container {
  text-align: center;

  .preview-image {
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
  }

  .single-image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 400px;
  }
}

/* 批量送达对话框样式 */
.batch-delivered-container {
  padding: 20px 0;
}

.upload-section {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  padding: 20px;
  text-align: center;
  background-color: #fafafa;
  transition: border-color 0.3s;
}

.upload-section:hover {
  border-color: #409EFF;
}

.upload-tip {
  margin-top: 10px;
  color: #666;
  font-size: 12px;
  line-height: 1.5;
}

.uploaded-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 10px;
}

.uploaded-image-item {
  position: relative;
  width: 80px;
  height: 80px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
}

.uploaded-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-index {
  position: absolute;
  top: 2px;
  left: 2px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 10px;
  padding: 1px 4px;
  border-radius: 2px;
}

.delete-image-btn {
  position: absolute;
  top: 2px;
  right: 2px;
  padding: 2px;
  min-height: auto;
  line-height: 1;
}

.upload-count-tip {
  margin-top: 10px;
  color: #409EFF;
  font-size: 12px;
  font-weight: bold;
}

.batch-voucher-uploader .el-upload {
  border: none;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

/* 批量回退对话框样式 */
.batch-back-container {
  padding: 20px 0;
}

.batch-back-container .el-textarea__inner {
  resize: none;
}

::v-deep .el-carousel__container {
  height: 400px;
}

::v-deep .el-carousel__item {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 订单操作记录按钮样式 */
.order-log-btn {
  color: #67c23a;
  cursor: pointer;
  transition: all 0.3s;
  padding: 2px 0;
}

.order-log-btn:hover {
  color: #5daf34;
  text-decoration: underline;
}

/* 订单操作记录弹窗样式 */
.order-log-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px 0;
}

.log-item {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  border-left: 4px solid #409eff;
  position: relative;
  transition: all 0.3s;
}

.log-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.log-item:last-child {
  margin-bottom: 0;
}

.log-time {
  font-size: 12px;
  color: #409eff;
  margin-bottom: 8px;
  font-weight: 500;
}

.log-content {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8px;
}

.log-detail {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
  background: #f0f0f0;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 8px;
  border-left: 2px solid #e0e0e0;
}

.no-log {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.no-log-text {
  font-size: 14px;
}

/* 弹窗滚动条样式 */
.order-log-content::-webkit-scrollbar {
  width: 6px;
}

.order-log-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.order-log-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.order-log-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 禁用状态的表格样式 */
.disabled-table {
  opacity: 0.5;
  pointer-events: none;
}

.disabled-table .el-table__body-wrapper {
  background-color: #f5f7fa;
}
</style>
