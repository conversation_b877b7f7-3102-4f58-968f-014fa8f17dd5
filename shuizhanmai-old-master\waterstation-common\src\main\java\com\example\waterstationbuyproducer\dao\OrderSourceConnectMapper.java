package com.example.waterstationbuyproducer.dao;

import com.example.waterstationbuyproducer.entity.OrderSourceConnect;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 订单来源关联表Mapper接口
 */
@Repository
public interface OrderSourceConnectMapper {

    /**
     * 新增关联关系
     */
    int insert(OrderSourceConnect record);

    /**
     * 根据ID删除关联关系
     */
    int deleteByPrimaryKey(Integer id);

    /**
     * 更新关联关系
     */
    int updateByPrimaryKey(OrderSourceConnect record);

    /**
     * 根据ID查询关联关系
     */
    OrderSourceConnect selectByPrimaryKey(Integer id);

    /**
     * 查询所有关联关系
     */
    List<OrderSourceConnect> selectAll();

    /**
     * 根据订单来源ID和unionCode查询关联关系
     */
    OrderSourceConnect selectByOrderSourceIdAndUnionCode(@Param("orderSourceId") Long orderSourceId, 
                                                         @Param("unionCode") String unionCode);

    /**
     * 根据订单来源ID查询所有关联关系
     */
    List<OrderSourceConnect> selectByOrderSourceId(@Param("orderSourceId") Long orderSourceId);

    /**
     * 根据产品ID查询所有关联关系
     */
    List<OrderSourceConnect> selectByProductNewId(@Param("productNewId") Long productNewId);

    /**
     * 根据unionCode查询关联关系
     */
    List<OrderSourceConnect> selectByUnionCode(@Param("unionCode") String unionCode);

    /**
     * 分页查询关联关系（支持搜索条件）
     */
    List<OrderSourceConnect> selectByPage(@Param("unionCode") String unionCode,
                                         @Param("name") String name,
                                         @Param("orderSourceId") Long orderSourceId,
                                         @Param("configStatus") String configStatus,
                                         @Param("offset") Integer offset,
                                         @Param("pageSize") Integer pageSize);

    /**
     * 统计查询结果总数（支持搜索条件）
     */
    Long countByConditions(@Param("unionCode") String unionCode,
                          @Param("name") String name,
                          @Param("orderSourceId") Long orderSourceId,
                          @Param("configStatus") String configStatus);
}
