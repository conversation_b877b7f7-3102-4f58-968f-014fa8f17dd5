package com.example.waterstationbuyproducer.szmb.vo.order;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 订单来源关联分页查询请求参数
 */
@ApiModel(description = "订单来源关联分页查询请求参数")
public class PageQueryReq {

    @ApiModelProperty(value = "页码，从1开始", example = "1")
    private Integer pageNo;

    @ApiModelProperty(value = "每页大小", example = "20")
    private Integer pageSize;

    @ApiModelProperty(value = "唯一标识(skuId)", example = "SKU123")
    private String unionCode;

    @ApiModelProperty(value = "名称（支持多关键词搜索，用空格分隔）", example = "农夫山泉 550ml")
    private String name;

    @ApiModelProperty(value = "订单来源ID", example = "1")
    private Long orderSourceId;

    @ApiModelProperty(value = "配置状态：configured-已配置，unconfigured-未配置", example = "configured")
    private String configStatus;

    public PageQueryReq() {
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public String getUnionCode() {
        return unionCode;
    }

    public void setUnionCode(String unionCode) {
        this.unionCode = unionCode;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getOrderSourceId() {
        return orderSourceId;
    }

    public void setOrderSourceId(Long orderSourceId) {
        this.orderSourceId = orderSourceId;
    }

    public String getConfigStatus() {
        return configStatus;
    }

    public void setConfigStatus(String configStatus) {
        this.configStatus = configStatus;
    }

    @Override
    public String toString() {
        return "PageQueryReq{" +
                "pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                ", unionCode='" + unionCode + '\'' +
                ", name='" + name + '\'' +
                ", orderSourceId=" + orderSourceId +
                ", configStatus='" + configStatus + '\'' +
                '}';
    }
}
