spring:
  profiles:
    active: test #main 正式 test

mp:
  appid: wxacd08658b461406e
  secret: 57e4f7009789307f2e55bbc523af6a22
  token: yingquan_yanfazhongxin
  aesKey: 0XdGlSlwvZ7tpnuTOmCAwt5JU94AlBtlSfR3Qjlsmc9

# SF Express API Configuration
sf:
  express:
    # API endpoint URL (sandbox mode by default)
    apiUrl: https://bspgw.sf-express.com/std/service # 正式环境
    checkWord: QHubSGD8YmByMSLSAarUjGJDITaySDHb # 正式环境
    # apiUrl: https://sfapi-sbox.sf-express.com/std/service # 测试环境
    # checkWord: oUOT4pUL84cPkUwEs1nieCKeyuVjh3X2 # 测试环境
    # Client code (Partner ID) - Replace with your actual client code
    clientCode: YQDZS7KIXFPK
    # Connection timeout in milliseconds
    connectTimeout: 30000
    # Socket timeout in milliseconds
    socketTimeout: 30000
    # Language setting
    language: zh-cn
    # Whether to enable sandbox mode
    sandboxMode: true