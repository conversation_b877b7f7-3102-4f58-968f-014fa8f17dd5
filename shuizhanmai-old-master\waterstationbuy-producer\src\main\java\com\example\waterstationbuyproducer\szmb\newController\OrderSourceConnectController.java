package com.example.waterstationbuyproducer.szmb.newController;

import com.example.waterstationbuyproducer.dao.OrderSourceConnectMapper;
import com.example.waterstationbuyproducer.entity.OrderSourceConnect;
import com.example.waterstationbuyproducer.util.ResultBean;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 订单来源关联表Controller
 * <AUTHOR>
 * @description 订单来源关联管理
 * @date 2025/01/20
 */
@RestController
@RequestMapping("szmb/orderSourceConnect")
@Api(value = "szmb/orderSourceConnect/", description = "订单来源关联管理接口")
public class OrderSourceConnectController {

    private static final Logger logger = LoggerFactory.getLogger(OrderSourceConnectController.class);

    @Autowired
    private OrderSourceConnectMapper orderSourceConnectMapper;

    /**
     * 查询所有关联关系
     */
    @GetMapping("all")
    @ApiOperation(value = "查询所有关联关系", httpMethod = "GET", response = ResultBean.class,
                  notes = "获取所有订单来源关联关系列表")
    public ResultBean all() {
        ResultBean resultBean = new ResultBean();
        try {
            List<OrderSourceConnect> list = orderSourceConnectMapper.selectAll();
            return resultBean.success(list);
        } catch (Exception e) {
            logger.error("查询所有关联关系失败", e);
            return resultBean.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 分页查询关联关系
     */
    @GetMapping("page")
    @ApiOperation(value = "分页查询关联关系", httpMethod = "GET", response = ResultBean.class,
                  notes = "支持搜索条件的分页查询订单来源关联关系")
    public ResultBean page(@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                          @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
                          @RequestParam(value = "unionCode", required = false) String unionCode,
                          @RequestParam(value = "name", required = false) String name,
                          @RequestParam(value = "orderSourceId", required = false) Long orderSourceId,
                          @RequestParam(value = "configStatus", required = false) String configStatus) {
        ResultBean resultBean = new ResultBean();
        try {
            // 参数校验
            if (pageNo == null || pageNo < 1) {
                pageNo = 1;
            }
            if (pageSize == null || pageSize < 1) {
                pageSize = 20;
            }
            if (pageSize > 100) {
                pageSize = 100; // 限制最大页面大小
            }

            // 计算偏移量
            int offset = (pageNo - 1) * pageSize;

            // 查询数据
            List<OrderSourceConnect> list = orderSourceConnectMapper.selectByPage(
                unionCode,
                name,
                orderSourceId,
                configStatus,
                offset,
                pageSize
            );

            // 查询总数
            Long total = orderSourceConnectMapper.countByConditions(
                unionCode,
                name,
                orderSourceId,
                configStatus
            );

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("list", list);
            result.put("total", total);
            result.put("pageNo", pageNo);
            result.put("pageSize", pageSize);
            result.put("pages", (total + pageSize - 1) / pageSize);

            return resultBean.success(result);
        } catch (Exception e) {
            logger.error("分页查询关联关系失败", e);
            return resultBean.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询关联关系
     */
    @GetMapping("detail/{id}")
    @ApiOperation(value = "根据ID查询关联关系", httpMethod = "GET", response = ResultBean.class,
                  notes = "根据关联关系ID获取详细信息")
    public ResultBean detail(@ApiParam(value = "关联关系ID", required = true) @PathVariable Integer id) {
        ResultBean resultBean = new ResultBean();
        try {
            if (id == null) {
                return resultBean.error("关联关系ID不能为空");
            }

            OrderSourceConnect connect = orderSourceConnectMapper.selectByPrimaryKey(id);
            if (connect == null) {
                return resultBean.error("关联关系不存在");
            }

            return resultBean.success(connect);
        } catch (Exception e) {
            logger.error("查询关联关系详情失败", e);
            return resultBean.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 新增关联关系
     */
    @PostMapping("add")
    @ApiOperation(value = "新增关联关系", httpMethod = "POST", response = ResultBean.class,
                  notes = "添加新的订单来源关联关系")
    public ResultBean add(@RequestBody OrderSourceConnect orderSourceConnect) {
        ResultBean resultBean = new ResultBean();
        try {
            if (orderSourceConnect == null) {
                return resultBean.error("关联关系信息不能为空");
            }

            if (orderSourceConnect.getUnionCode() == null || orderSourceConnect.getUnionCode().trim().isEmpty()) {
                return resultBean.error("唯一标识不能为空");
            }

            if (orderSourceConnect.getOrderSourceId() == null) {
                return resultBean.error("订单来源ID不能为空");
            }

            // 检查是否已存在相同的关联关系
            OrderSourceConnect existing = orderSourceConnectMapper.selectByOrderSourceIdAndUnionCode(
                orderSourceConnect.getOrderSourceId(), orderSourceConnect.getUnionCode());
            if (existing != null) {
                return resultBean.error("该订单来源和唯一标识的关联关系已存在");
            }

            // 设置创建时间和更新时间
            Date now = new Date();
            orderSourceConnect.setCreateTime(now);
            orderSourceConnect.setUpdateTime(now);

            int result = orderSourceConnectMapper.insert(orderSourceConnect);
            if (result > 0) {
                return resultBean.success("新增成功");
            } else {
                return resultBean.error("新增失败");
            }
        } catch (Exception e) {
            logger.error("新增关联关系失败", e);
            return resultBean.error("新增失败: " + e.getMessage());
        }
    }

    /**
     * 更新关联关系
     */
    @PostMapping("update")
    @ApiOperation(value = "更新关联关系", httpMethod = "POST", response = ResultBean.class,
                  notes = "更新订单来源关联关系信息")
    public ResultBean update(@RequestBody OrderSourceConnect orderSourceConnect) {
        ResultBean resultBean = new ResultBean();
        try {
            if (orderSourceConnect == null || orderSourceConnect.getId() == null) {
                return resultBean.error("关联关系ID不能为空");
            }

            // 检查关联关系是否存在
            OrderSourceConnect existing = orderSourceConnectMapper.selectByPrimaryKey(orderSourceConnect.getId());
            if (existing == null) {
                return resultBean.error("关联关系不存在");
            }

            if (orderSourceConnect.getUnionCode() == null || orderSourceConnect.getUnionCode().trim().isEmpty()) {
                return resultBean.error("唯一标识不能为空");
            }

            if (orderSourceConnect.getOrderSourceId() == null) {
                return resultBean.error("订单来源ID不能为空");
            }

            // 检查是否与其他记录冲突（排除自己）
            OrderSourceConnect conflict = orderSourceConnectMapper.selectByOrderSourceIdAndUnionCode(
                orderSourceConnect.getOrderSourceId(), orderSourceConnect.getUnionCode());
            if (conflict != null && !conflict.getId().equals(orderSourceConnect.getId())) {
                return resultBean.error("该订单来源和唯一标识的关联关系已存在");
            }

            // 设置更新时间
            orderSourceConnect.setUpdateTime(new Date());

            int result = orderSourceConnectMapper.updateByPrimaryKey(orderSourceConnect);
            if (result > 0) {
                return resultBean.success("更新成功");
            } else {
                return resultBean.error("更新失败");
            }
        } catch (Exception e) {
            logger.error("更新关联关系失败", e);
            return resultBean.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除关联关系
     */
    @PostMapping("delete/{id}")
    @ApiOperation(value = "删除关联关系", httpMethod = "POST", response = ResultBean.class,
                  notes = "删除指定的订单来源关联关系")
    public ResultBean delete(@ApiParam(value = "关联关系ID", required = true) @PathVariable Integer id) {
        ResultBean resultBean = new ResultBean();
        try {
            if (id == null) {
                return resultBean.error("关联关系ID不能为空");
            }

            // 检查关联关系是否存在
            OrderSourceConnect existing = orderSourceConnectMapper.selectByPrimaryKey(id);
            if (existing == null) {
                return resultBean.error("关联关系不存在");
            }

            int result = orderSourceConnectMapper.deleteByPrimaryKey(id);
            if (result > 0) {
                return resultBean.success("删除成功");
            } else {
                return resultBean.error("删除失败");
            }
        } catch (Exception e) {
            logger.error("删除关联关系失败", e);
            return resultBean.error("删除失败: " + e.getMessage());
        }
    }

    /**
     * 查询未配置product_new_id的关联关系
     * 用于后台管理，查看哪些关联关系需要补充产品信息
     */
    @GetMapping("listUnConfigured")
    @ApiOperation(value = "查询未配置产品的关联关系", httpMethod = "GET", response = ResultBean.class,
                  notes = "获取product_new_id为空的关联关系列表")
    public ResultBean listUnConfigured() {
        ResultBean resultBean = new ResultBean();
        try {
            List<OrderSourceConnect> allConnects = orderSourceConnectMapper.selectAll();
            List<OrderSourceConnect> unConfigured = new ArrayList<>();

            for (OrderSourceConnect connect : allConnects) {
                if (connect.getProductNewId() == null) {
                    unConfigured.add(connect);
                }
            }

            return resultBean.success(unConfigured);
        } catch (Exception e) {
            logger.error("查询未配置product_new_id的关联关系失败", e);
            return resultBean.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新product_new_id
     * 用于后台管理，批量配置产品关联关系
     */
    @PostMapping("batchUpdateProductId")
    @ApiOperation(value = "批量更新产品关联", httpMethod = "POST", response = ResultBean.class,
                  notes = "批量配置关联关系的产品ID")
    public ResultBean batchUpdateProductId(@RequestBody List<OrderSourceConnect> orderSourceConnects) {
        ResultBean resultBean = new ResultBean();
        try {
            int successCount = 0;
            for (OrderSourceConnect connect : orderSourceConnects) {
                if (connect.getId() != null && connect.getProductNewId() != null) {
                    connect.setUpdateTime(new Date());
                    int result = orderSourceConnectMapper.updateByPrimaryKey(connect);
                    if (result > 0) {
                        successCount++;
                    }
                }
            }

            return resultBean.success("批量更新完成，成功: " + successCount + "条，总计: " + orderSourceConnects.size() + "条");
        } catch (Exception e) {
            logger.error("批量更新product_new_id失败", e);
            return resultBean.error("批量更新失败: " + e.getMessage());
        }
    }

}
