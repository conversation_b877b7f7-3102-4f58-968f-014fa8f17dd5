# 订单来源关联分页重构说明

## 重构背景

原有的订单来源关联页面使用前端分页，存在严重的性能问题：
- 每次查询都获取所有数据到前端
- 在前端进行过滤、排序、分页处理
- 当数据量达到100万条时会导致严重的性能问题和内存占用

## 重构内容

### 1. 后端改动

#### 1.1 Mapper接口扩展
**文件**: `waterstation-common/src/main/java/com/example/waterstationbuyproducer/dao/OrderSourceConnectMapper.java`

新增方法：
```java
/**
 * 分页查询关联关系（支持搜索条件）
 */
List<OrderSourceConnect> selectByPage(@Param("unionCode") String unionCode,
                                     @Param("name") String name,
                                     @Param("orderSourceId") Long orderSourceId,
                                     @Param("configStatus") String configStatus,
                                     @Param("offset") Integer offset,
                                     @Param("pageSize") Integer pageSize);

/**
 * 统计查询结果总数（支持搜索条件）
 */
Long countByConditions(@Param("unionCode") String unionCode,
                      @Param("name") String name,
                      @Param("orderSourceId") Long orderSourceId,
                      @Param("configStatus") String configStatus);
```

#### 1.2 SQL映射文件扩展
**文件**: `waterstationbuy-producer/src/main/resources/mapper/OrderSourceConnectMapper.xml`

新增SQL查询：
- `selectByPage`: 支持多条件查询的分页SQL
- `countByConditions`: 对应的计数SQL
- 支持多关键词搜索（空格分隔）

#### 1.3 新增分页查询接口
**文件**: `waterstationbuy-producer/src/main/java/com/example/waterstationbuyproducer/szmb/newController/OrderSourceConnectController.java`

新增接口：
```java
@GetMapping("page")
public ResultBean page(@RequestParam(value = "pageNo", defaultValue = "1") Integer pageNo,
                      @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize,
                      @RequestParam(value = "unionCode", required = false) String unionCode,
                      @RequestParam(value = "name", required = false) String name,
                      @RequestParam(value = "orderSourceId", required = false) Long orderSourceId,
                      @RequestParam(value = "configStatus", required = false) String configStatus)
```

支持的URL参数：
- pageNo: 页码（默认1）
- pageSize: 每页大小（默认20）
- unionCode: 唯一标识搜索（可选）
- name: 名称搜索，支持多关键词（可选）
- orderSourceId: 订单来源ID（可选）
- configStatus: 配置状态（可选）

### 2. 前端改动

#### 2.1 数据获取方式重构
**文件**: `casaba-shuizhanmai-vue-master/business_platform_cs/src/views/setAdmin/orderSourceConnect.vue`

主要变更：
- `getDataList()` 方法改为调用 `/szmb/orderSourceConnect/page` 接口
- 移除前端的数据过滤、排序、分页逻辑
- 直接使用后端返回的分页数据

#### 2.2 导出功能优化
- `getAllDataForExport()` 方法改为使用分页接口获取数据
- 使用大页面大小（10000）来获取导出数据
- 保持搜索条件的一致性

## 功能特性

### 1. 多关键词搜索
- 支持空格分隔的多个关键词
- 所有关键词都必须匹配（AND逻辑）
- 不区分大小写
- 自动处理多余空格

**使用示例**：
- 输入 `"农夫山泉 550ml"` 会匹配同时包含"农夫山泉"和"550ml"的商品
- 输入 `"   水    桶   "` 会被处理为搜索"水"和"桶"

### 2. 后端分页
- 真正的数据库分页，避免大数据量的性能问题
- 支持灵活的页面大小设置（最大100条）
- 准确的总数统计

### 3. 搜索条件支持
- 唯一标识（unionCode）模糊搜索
- 名称多关键词搜索
- 订单来源精确筛选
- 配置状态筛选（已配置/未配置）

## 性能优化

### 1. 数据库层面
- 使用 LIMIT 进行真正的分页查询
- 避免全表扫描和大量数据传输
- 支持索引优化的查询条件

### 2. 应用层面
- 减少内存占用
- 降低网络传输量
- 提高页面响应速度

### 3. 用户体验
- 保持原有的搜索和分页功能
- 提示用户支持多关键词搜索
- 导出功能保持一致的搜索条件

## 接口说明

### 分页查询接口
**URL**: `GET /szmb/orderSourceConnect/page`

**请求参数** (URL参数):
```
GET /szmb/orderSourceConnect/page?pageNo=1&pageSize=20&unionCode=SKU123&name=农夫山泉 550ml&orderSourceId=1&configStatus=configured
```

**参数说明**:
- `pageNo`: 页码，默认1
- `pageSize`: 每页大小，默认20，最大100
- `unionCode`: 唯一标识，支持模糊搜索（可选）
- `name`: 名称，支持多关键词搜索，用空格分隔（可选）
- `orderSourceId`: 订单来源ID（可选）
- `configStatus`: 配置状态，值为 `configured` 或 `unconfigured`（可选）

**响应结果**:
```json
{
  "code": 1,
  "message": "success",
  "data": {
    "list": [...],
    "total": 1000,
    "pageNo": 1,
    "pageSize": 20,
    "pages": 50
  }
}
```

## 兼容性说明

- 保留原有的 `/szmb/orderSourceConnect/all` 接口，确保其他功能不受影响
- 前端界面和交互逻辑保持不变
- 搜索功能增强，向下兼容

## 测试建议

1. **功能测试**：验证搜索、分页、导出功能正常
2. **性能测试**：在大数据量下测试响应时间
3. **多关键词测试**：验证空格分隔的多关键词搜索
4. **边界测试**：测试空搜索条件、大页面大小等边界情况
