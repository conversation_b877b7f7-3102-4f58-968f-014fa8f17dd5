package com.example.waterstationbuyproducer.dto.sfexpress;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * SF Express Print Waybill Request
 * 顺丰快递打印面单请求对象
 *
 * <AUTHOR>
 * @date 2025-01-17
 */
@Data
public class SfExpressPrintWaybillRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * Language
     * 语言
     */
    private String language = "zh-cn";
    
    /**
     * Template Code
     * 模板编码
     */
    @JsonProperty("templateCode")
    private String templateCode;
    
    /**
     * Documents
     * 业务数据
     */
    @JsonProperty("documents")
    private List<Document> documents;


    @JsonProperty("extJson")
    private ExtJson extJson;
    
    /**
     * Version
     * 版本号
     */
    @JsonProperty("version")
    private String version = "2.0";

    @JsonProperty("fileType")
    private String fileType = "pdf";
    
    @JsonProperty("sync")
    private Boolean sync = true;
    
    /**
     * Document
     * 业务数据对象
     */
    @Data
    public static class Document implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * Master Waybill Number
         * 主运单号
         */
        @JsonProperty("masterWaybillNo")
        private String masterWaybillNo;
        
        /**
         * Branch Waybill Number
         * 子运单号
         */
        @JsonProperty("branchWaybillNo")
        private String branchWaybillNo;
        
        /**
         * Back Waybill Number
         * 备用运单号
         */
        @JsonProperty("backWaybillNo")
        private String backWaybillNo;
        
        /**
         * Sequence Number
         * 运单编号打印的序号，由用户自行维护，如果用户不维护，则由顺丰维护
         */
        @JsonProperty("seq")
        private String seq;
        
        /**
         * Sum
         * 子包裹总数量
         */
        @JsonProperty("sum")
        private String sum;
        
        /**
         * Is Print Logo
         * 如果数据库中上已印刷的COD及到付多元化服务不可打印，如果印刷请求
         * 打印，true:数据库中上已印刷的可以打印
         */
        @JsonProperty("isPrintLogo")
        private String isPrintLogo;
        
        /**
         * Remark
         * 自定义区域备注
         */
        @JsonProperty("remark")
        private String remark;
        
        /**
         * Waybill Number Check Type
         * 运单号校验类型：1、校验运单号是否存在；2、客户自主生成运单号，不校验运单号是否存在
         */
        @JsonProperty("waybillNoCheckType")
        private String waybillNoCheckType;
        
        /**
         * Waybill Number Check Value
         * 运单号校验数据 比如："123456"
         */
        @JsonProperty("waybillNoCheckValue")
        private String waybillNoCheckValue;
        
        /**
         * Custom Data
         * 使用自定义区域模板时，对应的变量数据，如果没有使用自定义区域模板，可以不传
         */
        @JsonProperty("customData")
        private String customData;
    }
    @Data
    public static class ExtJson implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * Master Waybill Number
         * 主运单号
         */
        @JsonProperty("mergePdf")
        private Boolean mergePdf = true;
        @JsonProperty("mergeType")
        private String mergeType = "all";
        
    }
}
